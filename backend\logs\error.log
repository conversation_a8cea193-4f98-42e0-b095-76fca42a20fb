<<<<<<< HEAD
{"error":"Message validation failed: content: Le message doit avoir du contenu ou au moins une pièce jointe","level":"error","message":"Send message error:","receiverId":"68294542b5d5c86fd2e56e4b","senderId":"68294132b5d5c86fd2e56e23","stack":"ValidationError: Message validation failed: content: Le message doit avoir du contenu ou au moins une pièce jointe\n    at Document.invalidate (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\document.js:3329:32)\n    at C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\document.js:3090:17\n    at C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at process.processTicksAndRejections (node:internal/process/task_queues:77:11)","timestamp":"2025-05-24 23:16:43"}
{"error":"Message validation failed: content: Le message doit avoir du contenu ou au moins une pièce jointe","level":"error","message":"Send message error:","receiverId":"68294542b5d5c86fd2e56e4b","senderId":"68294132b5d5c86fd2e56e23","stack":"ValidationError: Message validation failed: content: Le message doit avoir du contenu ou au moins une pièce jointe\n    at Document.invalidate (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\document.js:3329:32)\n    at C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\document.js:3090:17\n    at C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at process.processTicksAndRejections (node:internal/process/task_queues:77:11)","timestamp":"2025-05-24 23:16:43"}
{"error":"Message validation failed: content: Le message doit avoir du contenu ou au moins une pièce jointe","level":"error","message":"Send message error:","receiverId":"68294542b5d5c86fd2e56e4b","senderId":"68294132b5d5c86fd2e56e23","stack":"ValidationError: Message validation failed: content: Le message doit avoir du contenu ou au moins une pièce jointe\n    at Document.invalidate (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\document.js:3329:32)\n    at C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\document.js:3090:17\n    at C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at process.processTicksAndRejections (node:internal/process/task_queues:77:11)","timestamp":"2025-05-24 23:18:15"}
{"error":"Message validation failed: content: Le message doit avoir du contenu ou au moins une pièce jointe","level":"error","message":"Send message error:","receiverId":"68294542b5d5c86fd2e56e4b","senderId":"68294132b5d5c86fd2e56e23","stack":"ValidationError: Message validation failed: content: Le message doit avoir du contenu ou au moins une pièce jointe\n    at Document.invalidate (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\document.js:3329:32)\n    at C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\document.js:3090:17\n    at C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at process.processTicksAndRejections (node:internal/process/task_queues:77:11)","timestamp":"2025-05-24 23:18:15"}
{"level":"error","message":"POST / 500 - 26ms","timestamp":"2025-05-25 02:25:29"}
{"level":"error","message":"POST / 500 - 20ms","timestamp":"2025-05-25 05:12:02"}
{"level":"error","message":"POST / 500 - 23ms","timestamp":"2025-05-25 05:40:02"}
{"level":"error","message":"POST / 500 - 10ms","timestamp":"2025-05-25 05:47:56"}
{"level":"error","message":"POST /api/projets/create 500 - 22ms","timestamp":"2025-05-25 14:15:35"}
{"level":"error","message":"POST /api/projets/create 500 - 11ms","timestamp":"2025-05-25 14:23:17"}
{"level":"error","message":"POST /login 500 - 184ms","timestamp":"2025-05-25 15:26:03"}
{"level":"error","message":"PUT /users/68038b686229308a4e6b3e81/activation 500 - 22ms","timestamp":"2025-05-26 14:07:27"}
{"level":"error","message":"PUT /users/68038b686229308a4e6b3e81/activation 500 - 10ms","timestamp":"2025-05-26 14:07:34"}
{"level":"error","message":"PUT /users/68038b686229308a4e6b3e81/activation 500 - 8ms","timestamp":"2025-05-26 14:07:35"}
{"level":"error","message":"PUT /users/6803a3aea2987ba81b2d0d84/activation 500 - 6ms","timestamp":"2025-05-26 14:07:38"}
{"level":"error","message":"PUT /users/68038e6b11c5e2dbcff30509/activation 500 - 10ms","timestamp":"2025-05-26 14:11:16"}
{"level":"error","message":"PUT /users/680398ee77cf38097b72ecfd/activation 500 - 10ms","timestamp":"2025-05-26 14:16:11"}
{"level":"error","message":"PUT /users/6803a3d7a2987ba81b2d0d87/activation 500 - 8ms","timestamp":"2025-05-26 14:29:41"}
{"level":"error","message":"PUT /users/680395e3db62312bb24d1d20/activation 500 - 6ms","timestamp":"2025-05-26 14:33:44"}
{"level":"error","message":"PUT /users/68038e6b11c5e2dbcff30509/activation 500 - 13ms","timestamp":"2025-05-26 14:34:11"}
{"level":"error","message":"PUT /users/680395e3db62312bb24d1d20/activation 500 - 7ms","timestamp":"2025-05-26 14:34:17"}
{"level":"error","message":"PUT /users/680395e3db62312bb24d1d20/activation 500 - 11ms","timestamp":"2025-05-26 14:41:09"}
{"level":"error","message":"PUT /users/68038b686229308a4e6b3e81/activation 500 - 6ms","timestamp":"2025-05-26 15:23:45"}
<<<<<<< HEAD
{"level":"error","message":"PUT /users/6803a340671914c7396b6776/activation 500 - 12ms","timestamp":"2025-05-27 12:58:34"}
=======
{"level":"error","message":"POST /evaluations/683639e58d6ed3b9c5abb881 500 - 1771ms","timestamp":"2025-05-27 23:18:02"}
>>>>>>> 690c3be2dfd5893ca45bf15721dec11a04b0efb0
=======
{"error":"Message validation failed: content: Le message doit avoir du contenu ou au moins une pièce jointe","level":"error","message":"Send message error:","receiverId":"68294542b5d5c86fd2e56e4b","senderId":"68294132b5d5c86fd2e56e23","stack":"ValidationError: Message validation failed: content: Le message doit avoir du contenu ou au moins une pièce jointe\n    at Document.invalidate (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\document.js:3329:32)\n    at C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\document.js:3090:17\n    at C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at process.processTicksAndRejections (node:internal/process/task_queues:77:11)","timestamp":"2025-05-24 23:16:43"}
{"error":"Message validation failed: content: Le message doit avoir du contenu ou au moins une pièce jointe","level":"error","message":"Send message error:","receiverId":"68294542b5d5c86fd2e56e4b","senderId":"68294132b5d5c86fd2e56e23","stack":"ValidationError: Message validation failed: content: Le message doit avoir du contenu ou au moins une pièce jointe\n    at Document.invalidate (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\document.js:3329:32)\n    at C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\document.js:3090:17\n    at C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at process.processTicksAndRejections (node:internal/process/task_queues:77:11)","timestamp":"2025-05-24 23:16:43"}
{"error":"Message validation failed: content: Le message doit avoir du contenu ou au moins une pièce jointe","level":"error","message":"Send message error:","receiverId":"68294542b5d5c86fd2e56e4b","senderId":"68294132b5d5c86fd2e56e23","stack":"ValidationError: Message validation failed: content: Le message doit avoir du contenu ou au moins une pièce jointe\n    at Document.invalidate (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\document.js:3329:32)\n    at C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\document.js:3090:17\n    at C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at process.processTicksAndRejections (node:internal/process/task_queues:77:11)","timestamp":"2025-05-24 23:18:15"}
{"error":"Message validation failed: content: Le message doit avoir du contenu ou au moins une pièce jointe","level":"error","message":"Send message error:","receiverId":"68294542b5d5c86fd2e56e4b","senderId":"68294132b5d5c86fd2e56e23","stack":"ValidationError: Message validation failed: content: Le message doit avoir du contenu ou au moins une pièce jointe\n    at Document.invalidate (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\document.js:3329:32)\n    at C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\document.js:3090:17\n    at C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at process.processTicksAndRejections (node:internal/process/task_queues:77:11)","timestamp":"2025-05-24 23:18:15"}
{"level":"error","message":"POST / 500 - 26ms","timestamp":"2025-05-25 02:25:29"}
{"level":"error","message":"POST / 500 - 20ms","timestamp":"2025-05-25 05:12:02"}
{"level":"error","message":"POST / 500 - 23ms","timestamp":"2025-05-25 05:40:02"}
{"level":"error","message":"POST / 500 - 10ms","timestamp":"2025-05-25 05:47:56"}
{"level":"error","message":"POST /api/projets/create 500 - 22ms","timestamp":"2025-05-25 14:15:35"}
{"level":"error","message":"POST /api/projets/create 500 - 11ms","timestamp":"2025-05-25 14:23:17"}
{"level":"error","message":"POST /login 500 - 184ms","timestamp":"2025-05-25 15:26:03"}
{"level":"error","message":"PUT /users/68038b686229308a4e6b3e81/activation 500 - 22ms","timestamp":"2025-05-26 14:07:27"}
{"level":"error","message":"PUT /users/68038b686229308a4e6b3e81/activation 500 - 10ms","timestamp":"2025-05-26 14:07:34"}
{"level":"error","message":"PUT /users/68038b686229308a4e6b3e81/activation 500 - 8ms","timestamp":"2025-05-26 14:07:35"}
{"level":"error","message":"PUT /users/6803a3aea2987ba81b2d0d84/activation 500 - 6ms","timestamp":"2025-05-26 14:07:38"}
{"level":"error","message":"PUT /users/68038e6b11c5e2dbcff30509/activation 500 - 10ms","timestamp":"2025-05-26 14:11:16"}
{"level":"error","message":"PUT /users/680398ee77cf38097b72ecfd/activation 500 - 10ms","timestamp":"2025-05-26 14:16:11"}
{"level":"error","message":"PUT /users/6803a3d7a2987ba81b2d0d87/activation 500 - 8ms","timestamp":"2025-05-26 14:29:41"}
{"level":"error","message":"PUT /users/680395e3db62312bb24d1d20/activation 500 - 6ms","timestamp":"2025-05-26 14:33:44"}
{"level":"error","message":"PUT /users/68038e6b11c5e2dbcff30509/activation 500 - 13ms","timestamp":"2025-05-26 14:34:11"}
{"level":"error","message":"PUT /users/680395e3db62312bb24d1d20/activation 500 - 7ms","timestamp":"2025-05-26 14:34:17"}
{"level":"error","message":"PUT /users/680395e3db62312bb24d1d20/activation 500 - 11ms","timestamp":"2025-05-26 14:41:09"}
{"level":"error","message":"PUT /users/68038b686229308a4e6b3e81/activation 500 - 6ms","timestamp":"2025-05-26 15:23:45"}
{"level":"error","message":"POST /evaluations/683639e58d6ed3b9c5abb881 500 - 1771ms","timestamp":"2025-05-27 23:18:02"}
{"level":"error","message":"POST /evaluations/683658c7f892d6e57f7a4ef0 500 - 415ms","timestamp":"2025-05-28 01:29:39"}
{"level":"error","message":"POST /evaluations/683658c7f892d6e57f7a4ef0 500 - 29ms","timestamp":"2025-05-28 01:29:46"}
{"level":"error","message":"POST /evaluations/683658c7f892d6e57f7a4ef0 500 - 20ms","timestamp":"2025-05-28 01:29:47"}
{"level":"error","message":"POST /evaluations/683658c7f892d6e57f7a4ef0 500 - 23ms","timestamp":"2025-05-28 01:29:53"}
>>>>>>> 1befb6019fa5b6c1bb489eec821ae8316a2b9c1a
