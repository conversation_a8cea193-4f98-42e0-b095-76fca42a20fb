{"ast": null, "code": "import { throwError } from 'rxjs';\nimport { catchError } from 'rxjs/operators';\nimport { environment } from 'src/environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class EvaluationService {\n  constructor(http) {\n    this.http = http;\n  }\n  getAllEvaluations() {\n    const url = `${environment.urlBackend}evaluations/getev`;\n    return this.http.get(url).pipe(catchError(error => {\n      console.error('Erreur HTTP lors de la récupération des évaluations:', error);\n      return throwError(() => new Error('Erreur lors de la récupération des évaluations'));\n    }));\n  }\n  getEvaluationById(id) {\n    const url = `${environment.urlBackend}evaluations/${id}`;\n    return this.http.get(url).pipe(catchError(error => {\n      console.error('Erreur HTTP lors de la récupération de l\\'évaluation:', error);\n      return throwError(() => new Error('Erreur lors de la récupération de l\\'évaluation'));\n    }));\n  }\n  // Autres méthodes du service...\n  // Ajouter cette méthode pour mettre à jour les groupes manquants\n  updateMissingGroups() {\n    const url = `${environment.urlBackend}evaluations/update-missing-groups`;\n    return this.http.post(url, {}).pipe(catchError(error => {\n      console.error('Erreur HTTP lors de la mise à jour des groupes:', error);\n      return throwError(() => new Error('Erreur lors de la mise à jour des groupes'));\n    }));\n  }\n  // Méthode pour supprimer une évaluation\n  deleteEvaluation(evaluationId) {\n    const url = `${environment.urlBackend}evaluations/${evaluationId}`;\n    return this.http.delete(url).pipe(catchError(error => {\n      console.error('Erreur HTTP lors de la suppression de l\\'évaluation:', error);\n      return throwError(() => new Error('Erreur lors de la suppression de l\\'évaluation'));\n    }));\n  }\n  static {\n    this.ɵfac = function EvaluationService_Factory(t) {\n      return new (t || EvaluationService)(i0.ɵɵinject(i1.HttpClient));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: EvaluationService,\n      factory: EvaluationService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["throwError", "catchError", "environment", "EvaluationService", "constructor", "http", "getAllEvaluations", "url", "urlBackend", "get", "pipe", "error", "console", "Error", "getEvaluationById", "id", "updateMissingGroups", "post", "deleteEvaluation", "evaluationId", "delete", "i0", "ɵɵinject", "i1", "HttpClient", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\frontend\\src\\app\\services\\evaluation.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { HttpClient } from '@angular/common/http';\r\nimport { Observable, throwError } from 'rxjs';\r\nimport { catchError, map } from 'rxjs/operators';\r\nimport { environment } from 'src/environments/environment';\r\nimport { Evaluation } from '../models/evaluation';\r\n\r\n// Interface pour les évaluations avec détails\r\ninterface EvaluationWithDetails extends Evaluation {\r\n  renduDetails?: any;\r\n  etudiant?: any;\r\n  projetDetails?: any;\r\n}\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class EvaluationService {\r\n  constructor(private http: HttpClient) { }\r\n\r\n  getAllEvaluations(): Observable<EvaluationWithDetails[]> {\r\n    const url = `${environment.urlBackend}evaluations/getev`;\r\n\r\n    return this.http.get<EvaluationWithDetails[]>(url).pipe(\r\n      catchError(error => {\r\n        console.error('Erreur HTTP lors de la récupération des évaluations:', error);\r\n        return throwError(() => new Error('Erreur lors de la récupération des évaluations'));\r\n      })\r\n    );\r\n  }\r\n\r\n  getEvaluationById(id: string): Observable<Evaluation> {\r\n    const url = `${environment.urlBackend}evaluations/${id}`;\r\n\r\n    return this.http.get<Evaluation>(url).pipe(\r\n      catchError(error => {\r\n        console.error('Erreur HTTP lors de la récupération de l\\'évaluation:', error);\r\n        return throwError(() => new Error('Erreur lors de la récupération de l\\'évaluation'));\r\n      })\r\n    );\r\n  }\r\n\r\n  // Autres méthodes du service...\r\n\r\n  // Ajouter cette méthode pour mettre à jour les groupes manquants\r\n  updateMissingGroups(): Observable<any> {\r\n    const url = `${environment.urlBackend}evaluations/update-missing-groups`;\r\n\r\n    return this.http.post<any>(url, {}).pipe(\r\n      catchError(error => {\r\n        console.error('Erreur HTTP lors de la mise à jour des groupes:', error);\r\n        return throwError(() => new Error('Erreur lors de la mise à jour des groupes'));\r\n      })\r\n    );\r\n  }\r\n\r\n  // Méthode pour supprimer une évaluation\r\n  deleteEvaluation(evaluationId: string): Observable<any> {\r\n    const url = `${environment.urlBackend}evaluations/${evaluationId}`;\r\n\r\n    return this.http.delete<any>(url).pipe(\r\n      catchError(error => {\r\n        console.error('Erreur HTTP lors de la suppression de l\\'évaluation:', error);\r\n        return throwError(() => new Error('Erreur lors de la suppression de l\\'évaluation'));\r\n      })\r\n    );\r\n  }\r\n}\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n"], "mappings": "AAEA,SAAqBA,UAAU,QAAQ,MAAM;AAC7C,SAASC,UAAU,QAAa,gBAAgB;AAChD,SAASC,WAAW,QAAQ,8BAA8B;;;AAa1D,OAAM,MAAOC,iBAAiB;EAC5BC,YAAoBC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;EAAgB;EAExCC,iBAAiBA,CAAA;IACf,MAAMC,GAAG,GAAG,GAAGL,WAAW,CAACM,UAAU,mBAAmB;IAExD,OAAO,IAAI,CAACH,IAAI,CAACI,GAAG,CAA0BF,GAAG,CAAC,CAACG,IAAI,CACrDT,UAAU,CAACU,KAAK,IAAG;MACjBC,OAAO,CAACD,KAAK,CAAC,sDAAsD,EAAEA,KAAK,CAAC;MAC5E,OAAOX,UAAU,CAAC,MAAM,IAAIa,KAAK,CAAC,gDAAgD,CAAC,CAAC;IACtF,CAAC,CAAC,CACH;EACH;EAEAC,iBAAiBA,CAACC,EAAU;IAC1B,MAAMR,GAAG,GAAG,GAAGL,WAAW,CAACM,UAAU,eAAeO,EAAE,EAAE;IAExD,OAAO,IAAI,CAACV,IAAI,CAACI,GAAG,CAAaF,GAAG,CAAC,CAACG,IAAI,CACxCT,UAAU,CAACU,KAAK,IAAG;MACjBC,OAAO,CAACD,KAAK,CAAC,uDAAuD,EAAEA,KAAK,CAAC;MAC7E,OAAOX,UAAU,CAAC,MAAM,IAAIa,KAAK,CAAC,iDAAiD,CAAC,CAAC;IACvF,CAAC,CAAC,CACH;EACH;EAEA;EAEA;EACAG,mBAAmBA,CAAA;IACjB,MAAMT,GAAG,GAAG,GAAGL,WAAW,CAACM,UAAU,mCAAmC;IAExE,OAAO,IAAI,CAACH,IAAI,CAACY,IAAI,CAAMV,GAAG,EAAE,EAAE,CAAC,CAACG,IAAI,CACtCT,UAAU,CAACU,KAAK,IAAG;MACjBC,OAAO,CAACD,KAAK,CAAC,iDAAiD,EAAEA,KAAK,CAAC;MACvE,OAAOX,UAAU,CAAC,MAAM,IAAIa,KAAK,CAAC,2CAA2C,CAAC,CAAC;IACjF,CAAC,CAAC,CACH;EACH;EAEA;EACAK,gBAAgBA,CAACC,YAAoB;IACnC,MAAMZ,GAAG,GAAG,GAAGL,WAAW,CAACM,UAAU,eAAeW,YAAY,EAAE;IAElE,OAAO,IAAI,CAACd,IAAI,CAACe,MAAM,CAAMb,GAAG,CAAC,CAACG,IAAI,CACpCT,UAAU,CAACU,KAAK,IAAG;MACjBC,OAAO,CAACD,KAAK,CAAC,sDAAsD,EAAEA,KAAK,CAAC;MAC5E,OAAOX,UAAU,CAAC,MAAM,IAAIa,KAAK,CAAC,gDAAgD,CAAC,CAAC;IACtF,CAAC,CAAC,CACH;EACH;;;uBAjDWV,iBAAiB,EAAAkB,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;IAAA;EAAA;;;aAAjBrB,iBAAiB;MAAAsB,OAAA,EAAjBtB,iBAAiB,CAAAuB,IAAA;MAAAC,UAAA,EAFhB;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}