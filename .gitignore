# Environment files
.env
backend/.env
backend/.env.local

# Node.js
node_modules/
npm-debug.log
yarn-debug.log
yarn-error.log

# Backend specific (Node.js, Apollo Server)
backend/node_modules/
backend/dist/

# Uploads
uploads/

# IDEs and editors
.vscode/
.idea/
*.iml

# System files
.DS_Store
Thumbs.db
desktop.ini

# Logs
npm-debug.log
yarn-debug.log
yarn-error.log

# Compiled output
/dist
/tmp
/out-tsc
/bazel-out

# Node
/node_modules
npm-debug.log
yarn-error.log

# IDEs and editors
.idea/
.project
.classpath
.c9/
*.launch
.settings/
*.sublime-workspace

# Visual Studio Code
.vscode/*
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json
.history/*

# Miscellaneous
/.angular/cache
.sass-cache/
/connect.lock
/coverage
/libpeerconnection.log
testem.log
/typings

# System files
.DS_Store
Thumbs.db


backend/logs/*.log

