const User = require("../models/User");
const Group = require("../models/Group");
const bcrypt = require("bcryptjs");
const { generateSimplePassword } = require("../utils/generatePassword");
const { sendUserCreationEmail } = require("../utils/sendEmail");

// GET /api/admin/users
exports.getAllUsers = async (req, res) => {
  console.log("Admin Access by:", req.user);

  try {
    const users = await User.find()
      .select("-password -verificationCode -resetCode")
      .populate("group", "name description");

    res.json(users);
  } catch (err) {
    res.status(500).json({ message: "Server error", error: err.message });
  }
};

// PUT /api/admin/users/:id/role
exports.updateUserRole = async (req, res) => {
  const { id } = req.params;
  const { role } = req.body;

  if (!["student", "teacher", "admin"].includes(role)) {
    return res.status(400).json({ message: "Invalid role" });
  }

  try {
    const user = await User.findById(id);
    if (!user) return res.status(404).json({ message: "User not found" });

    user.role = role;
    await user.save();

    // Get updated user with group info
    const updatedUser = await User.findById(id)
      .select("-password -verificationCode -resetCode")
      .populate("group", "name description");

    res.json({
      message: "User role updated successfully",
      user: updatedUser,
    });
  } catch (err) {
    res.status(500).json({ message: "Server error", error: err.message });
  }
};

// PUT /api/admin/users/:id/activation
exports.toggleUserActivation = async (req, res) => {
  const { id } = req.params;
  const { isActive } = req.body;

  if (typeof isActive !== 'boolean') {
    return res.status(400).json({ message: "isActive must be a boolean value" });
  }

  try {
    const user = await User.findById(id);
    if (!user) return res.status(404).json({ message: "User not found" });

    // Prevent admin from deactivating themselves
    if (req.user.id === id && !isActive) {
      return res.status(400).json({
        message: "You cannot deactivate your own account"
      });
    }

    user.isActive = isActive;
    await user.save();

    // Get updated user with group info
    const updatedUser = await User.findById(id)
      .select("-password -verificationCode -resetCode")
      .populate("group", "name description");

    const action = isActive ? "activated" : "deactivated";
    res.json({
      message: `User ${action} successfully`,
      user: updatedUser,
    });
  } catch (err) {
    res.status(500).json({ message: "Server error", error: err.message });
  }
};

// PUT /api/admin/users/:id/group
exports.updateUserGroup = async (req, res) => {
  const { id } = req.params;
  const { groupId } = req.body;

  try {
    // Check if group exists if groupId is provided
    if (groupId) {
      const group = await Group.findById(groupId);
      if (!group) {
        return res.status(404).json({ message: "Group not found" });
      }
    }

    const user = await User.findById(id);
    if (!user) return res.status(404).json({ message: "User not found" });

    // Set group or remove if null
    user.group = groupId || null;
    await user.save();

    // Get updated user with group info
    const updatedUser = await User.findById(id)
      .select("-password -verificationCode -resetCode")
      .populate("group", "name description");

    res.json({
      message: "User group updated successfully",
      user: updatedUser,
    });
  } catch (err) {
    res.status(500).json({ message: "Server error", error: err.message });
  }
};

// POST /api/admin/users
exports.createUser = async (req, res) => {
  const { fullName, email, role = "student", groupId } = req.body;

  try {
    // Validate required fields
    if (!fullName || !email) {
      return res.status(400).json({
        message: "Full name and email are required"
      });
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return res.status(400).json({
        message: "Please provide a valid email address"
      });
    }

    // Validate role
    const validRoles = ["student", "teacher", "admin"];
    if (!validRoles.includes(role)) {
      return res.status(400).json({
        message: "Invalid role. Must be student, teacher, or admin"
      });
    }

    // Check if user already exists
    const existingUser = await User.findOne({ email: email.toLowerCase() });
    if (existingUser) {
      return res.status(400).json({
        message: "A user with this email already exists"
      });
    }

    // Check if group exists if groupId is provided
    if (groupId) {
      const group = await Group.findById(groupId);
      if (!group) {
        return res.status(404).json({ message: "Group not found" });
      }
    }

    // Generate a secure password
    const generatedPassword = generateSimplePassword(12);
    const hashedPassword = await bcrypt.hash(generatedPassword, 10);

    // Create new user
    const newUser = new User({
      fullName: fullName.trim(),
      email: email.toLowerCase().trim(),
      password: hashedPassword,
      role,
      group: groupId || null,
      verified: true, // Admin-created users are automatically verified
      isActive: true,
      isOnline: false,
      profileImage: process.env.DEFAULT_IMAGE || 'uploads/default.png'
    });

    await newUser.save();

    // Send welcome email with credentials
    try {
      await sendUserCreationEmail(email, fullName, generatedPassword, role);
    } catch (emailError) {
      console.error("Failed to send user creation email:", emailError);
      // Don't fail the user creation if email fails
    }

    // Get the created user with group info (excluding sensitive data)
    const createdUser = await User.findById(newUser._id)
      .select("-password -verificationCode -resetCode")
      .populate("group", "name description");

    res.status(201).json({
      message: "User created successfully! Login credentials have been sent via email.",
      user: createdUser,
      credentials: {
        email: email,
        password: generatedPassword, // Include in response for admin reference
        loginUrl: process.env.FRONTEND_URL || "http://localhost:4200/login"
      }
    });

  } catch (err) {
    console.error("Error creating user:", err);
    res.status(500).json({
      message: "Server error while creating user",
      error: err.message
    });
  }
};