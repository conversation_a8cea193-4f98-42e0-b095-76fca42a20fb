{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/services/auth.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"src/app/services/toast.service\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@angular/forms\";\nfunction DashboardComponent_div_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 18)(1, \"div\", 19)(2, \"div\", 20);\n    i0.ɵɵelement(3, \"i\", 21)(4, \"div\", 22);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 23)(6, \"h3\", 24);\n    i0.ɵɵtext(7, \" Success \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"p\", 25);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.message, \" \");\n  }\n}\nfunction DashboardComponent_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 26)(1, \"div\", 19)(2, \"div\", 27);\n    i0.ɵɵelement(3, \"i\", 28)(4, \"div\", 29);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 23)(6, \"h3\", 30);\n    i0.ɵɵtext(7, \" Error \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"p\", 31);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.error, \" \");\n  }\n}\nfunction DashboardComponent_div_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 32)(1, \"div\", 33);\n    i0.ɵɵelement(2, \"div\", 34)(3, \"div\", 35);\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction DashboardComponent_div_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 36)(1, \"div\", 37);\n    i0.ɵɵelement(2, \"div\", 38)(3, \"div\", 39);\n    i0.ɵɵelementStart(4, \"div\", 40)(5, \"div\", 41)(6, \"div\", 23)(7, \"div\", 42);\n    i0.ɵɵtext(8, \" Total Users \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 43);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"div\", 44);\n    i0.ɵɵelement(12, \"div\", 45);\n    i0.ɵɵelementStart(13, \"div\", 46);\n    i0.ɵɵelement(14, \"i\", 47);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(15, \"div\", 48);\n    i0.ɵɵelement(16, \"div\", 49);\n    i0.ɵɵelementStart(17, \"div\", 50);\n    i0.ɵɵelement(18, \"div\", 51);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(19, \"div\", 37);\n    i0.ɵɵelement(20, \"div\", 52)(21, \"div\", 53);\n    i0.ɵɵelementStart(22, \"div\", 40)(23, \"div\", 41)(24, \"div\", 23)(25, \"div\", 42);\n    i0.ɵɵtext(26, \" User Status \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"div\", 54)(28, \"div\", 41)(29, \"div\", 55);\n    i0.ɵɵelement(30, \"div\", 56);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(31, \"span\", 57);\n    i0.ɵɵtext(32);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(33, \"div\", 41);\n    i0.ɵɵelement(34, \"div\", 58);\n    i0.ɵɵelementStart(35, \"span\", 59);\n    i0.ɵɵtext(36);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(37, \"div\", 44);\n    i0.ɵɵelement(38, \"div\", 60);\n    i0.ɵɵelementStart(39, \"div\", 46);\n    i0.ɵɵelement(40, \"i\", 61);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(41, \"div\", 48);\n    i0.ɵɵelement(42, \"div\", 62);\n    i0.ɵɵelementStart(43, \"div\", 63)(44, \"div\", 64);\n    i0.ɵɵelement(45, \"div\", 65);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(46, \"div\", 66);\n    i0.ɵɵelement(47, \"div\", 67);\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(48, \"div\", 37);\n    i0.ɵɵelement(49, \"div\", 68)(50, \"div\", 69);\n    i0.ɵɵelementStart(51, \"div\", 40)(52, \"div\", 41)(53, \"div\", 23)(54, \"div\", 42);\n    i0.ɵɵtext(55, \" User Roles \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(56, \"div\", 70)(57, \"div\", 41);\n    i0.ɵɵelement(58, \"div\", 71);\n    i0.ɵɵelementStart(59, \"span\", 72);\n    i0.ɵɵtext(60);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(61, \"div\", 41);\n    i0.ɵɵelement(62, \"div\", 73);\n    i0.ɵɵelementStart(63, \"span\", 74);\n    i0.ɵɵtext(64);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(65, \"div\", 41);\n    i0.ɵɵelement(66, \"div\", 75);\n    i0.ɵɵelementStart(67, \"span\", 76);\n    i0.ɵɵtext(68);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(69, \"div\", 44);\n    i0.ɵɵelement(70, \"div\", 77);\n    i0.ɵɵelementStart(71, \"div\", 46);\n    i0.ɵɵelement(72, \"i\", 78);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(73, \"div\", 48);\n    i0.ɵɵelement(74, \"div\", 79);\n    i0.ɵɵelementStart(75, \"div\", 63)(76, \"div\", 80);\n    i0.ɵɵelement(77, \"div\", 81);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(78, \"div\", 82);\n    i0.ɵɵelement(79, \"div\", 83);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(80, \"div\", 84);\n    i0.ɵɵelement(81, \"div\", 85);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(82, \"div\", 86)(83, \"span\");\n    i0.ɵɵtext(84, \"Students\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(85, \"span\");\n    i0.ɵɵtext(86, \"Teachers\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(87, \"span\");\n    i0.ɵɵtext(88, \"Admins\");\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(10);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.users.length, \" \");\n    i0.ɵɵadvance(22);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r3.getActiveCount(), \" Active\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r3.getInactiveCount(), \" Inactive\");\n    i0.ɵɵadvance(8);\n    i0.ɵɵstyleProp(\"width\", ctx_r3.users.length ? ctx_r3.getActiveCount() / ctx_r3.users.length * 100 : 0, \"%\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵstyleProp(\"width\", ctx_r3.users.length ? ctx_r3.getInactiveCount() / ctx_r3.users.length * 100 : 0, \"%\");\n    i0.ɵɵadvance(14);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r3.getStudentCount(), \" Students\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r3.getTeacherCount(), \" Teachers\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r3.getAdminCount(), \" Admins\");\n    i0.ɵɵadvance(8);\n    i0.ɵɵstyleProp(\"width\", ctx_r3.users.length ? ctx_r3.getStudentCount() / ctx_r3.users.length * 100 : 0, \"%\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵstyleProp(\"width\", ctx_r3.users.length ? ctx_r3.getTeacherCount() / ctx_r3.users.length * 100 : 0, \"%\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵstyleProp(\"width\", ctx_r3.users.length ? ctx_r3.getAdminCount() / ctx_r3.users.length * 100 : 0, \"%\");\n  }\n}\nfunction DashboardComponent_div_28_button_13_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 100);\n    i0.ɵɵlistener(\"click\", function DashboardComponent_div_28_button_13_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r9 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r9.clearSearch());\n    });\n    i0.ɵɵelement(1, \"i\", 101);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DashboardComponent_div_28_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 87)(1, \"button\", 88);\n    i0.ɵɵlistener(\"click\", function DashboardComponent_div_28_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r11 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r11.openCreateUserModal());\n    });\n    i0.ɵɵelement(2, \"div\", 89)(3, \"div\", 90);\n    i0.ɵɵelementStart(4, \"div\", 91);\n    i0.ɵɵelement(5, \"i\", 92);\n    i0.ɵɵtext(6, \" Create User \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 93)(8, \"div\", 94);\n    i0.ɵɵelement(9, \"i\", 95);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"input\", 96);\n    i0.ɵɵlistener(\"input\", function DashboardComponent_div_28_Template_input_input_10_listener($event) {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r13 = i0.ɵɵnextContext();\n      ctx_r13.searchTerm = $event.target.value;\n      return i0.ɵɵresetView(ctx_r13.searchUsers());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"div\", 97);\n    i0.ɵɵelement(12, \"div\", 98);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(13, DashboardComponent_div_28_button_13_Template, 2, 0, \"button\", 99);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"value\", ctx_r4.searchTerm);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.searchTerm);\n  }\n}\nfunction DashboardComponent_div_29_tr_27_option_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 140);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"titlecase\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const role_r17 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", role_r17);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 2, role_r17), \" \");\n  }\n}\nfunction DashboardComponent_div_29_tr_27_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r19 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\", 117)(1, \"td\", 118)(2, \"div\", 41)(3, \"div\", 119);\n    i0.ɵɵelement(4, \"div\", 120);\n    i0.ɵɵelementStart(5, \"span\", 7);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 121)(8, \"div\", 122);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(10, \"td\", 123)(11, \"div\", 124);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"td\", 125)(14, \"span\", 126);\n    i0.ɵɵelement(15, \"i\");\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(17, \"td\", 125)(18, \"span\", 127);\n    i0.ɵɵelement(19, \"i\");\n    i0.ɵɵtext(20);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(21, \"td\", 125)(22, \"div\", 128)(23, \"select\", 129);\n    i0.ɵɵlistener(\"change\", function DashboardComponent_div_29_tr_27_Template_select_change_23_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r19);\n      const user_r15 = restoredCtx.$implicit;\n      const ctx_r18 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r18.onRoleChange(user_r15._id, $event.target.value));\n    });\n    i0.ɵɵtemplate(24, DashboardComponent_div_29_tr_27_option_24_Template, 3, 4, \"option\", 130);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(25, \"div\", 131);\n    i0.ɵɵelement(26, \"i\", 132);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(27, \"td\", 125)(28, \"button\", 133);\n    i0.ɵɵlistener(\"click\", function DashboardComponent_div_29_tr_27_Template_button_click_28_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r19);\n      const user_r15 = restoredCtx.$implicit;\n      const ctx_r20 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r20.toggleUserActivation(user_r15._id, user_r15.isActive !== false));\n    });\n    i0.ɵɵelement(29, \"div\", 134)(30, \"i\", 135);\n    i0.ɵɵelementStart(31, \"span\", 7);\n    i0.ɵɵtext(32);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(33, \"td\", 125)(34, \"button\", 136);\n    i0.ɵɵlistener(\"click\", function DashboardComponent_div_29_tr_27_Template_button_click_34_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r19);\n      const user_r15 = restoredCtx.$implicit;\n      const ctx_r21 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r21.onDeleteUser(user_r15._id));\n    });\n    i0.ɵɵelement(35, \"i\", 137);\n    i0.ɵɵtext(36, \" Delete \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(37, \"td\", 125)(38, \"button\", 138);\n    i0.ɵɵlistener(\"click\", function DashboardComponent_div_29_tr_27_Template_button_click_38_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r19);\n      const user_r15 = restoredCtx.$implicit;\n      const ctx_r22 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r22.showUserDetails(user_r15._id));\n    });\n    i0.ɵɵelement(39, \"i\", 139);\n    i0.ɵɵtext(40, \" Details \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const user_r15 = ctx.$implicit;\n    const ctx_r14 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(user_r15.fullName.charAt(0));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", user_r15.fullName, \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", user_r15.email, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", user_r15.verified ? \"bg-[#afcf75]/10 dark:bg-[#afcf75]/5 text-[#2a5a03] dark:text-[#afcf75]\" : \"bg-[#ff6b69]/10 dark:bg-[#ff6b69]/5 text-[#ff6b69] dark:text-[#ff8785]\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassMap(user_r15.verified ? \"fas fa-check-circle mr-1\" : \"fas fa-times-circle mr-1\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", user_r15.verified ? \"Yes\" : \"No\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", user_r15.isActive !== false ? \"bg-[#afcf75]/15 dark:bg-[#afcf75]/10 text-[#2a5a03] dark:text-[#afcf75] border border-[#afcf75]/30 shadow-sm\" : \"bg-[#ff6b69]/15 dark:bg-[#ff6b69]/10 text-[#ff6b69] dark:text-[#ff8785] border border-[#ff6b69]/30 shadow-sm\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassMap(user_r15.isActive !== false ? \"fas fa-check-circle mr-1.5 text-[10px]\" : \"fas fa-times-circle mr-1.5 text-[10px]\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", user_r15.isActive !== false ? \"Active\" : \"Deactivated\", \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"value\", user_r15.role);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r14.roles);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngClass\", user_r15.isActive !== false ? \"bg-[#ff6b69]/10 dark:bg-[#ff6b69]/5 text-[#ff6b69] dark:text-[#ff8785] hover:bg-[#ff6b69]/20 dark:hover:bg-[#ff6b69]/10 border-[#ff6b69]/30 hover:border-[#ff6b69]/50\" : \"bg-[#afcf75]/10 dark:bg-[#afcf75]/5 text-[#2a5a03] dark:text-[#afcf75] hover:bg-[#afcf75]/20 dark:hover:bg-[#afcf75]/10 border-[#afcf75]/30 hover:border-[#afcf75]/50\")(\"title\", user_r15.isActive !== false ? \"Click to deactivate this user account\" : \"Click to activate this user account\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", user_r15.isActive !== false ? \"bg-gradient-to-r from-[#ff6b69]/5 to-[#ff6b69]/10\" : \"bg-gradient-to-r from-[#afcf75]/5 to-[#afcf75]/10\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassMap(user_r15.isActive !== false ? \"fas fa-user-slash\" : \"fas fa-user-check\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", user_r15.isActive !== false ? \"Deactivate\" : \"Activate\", \" \");\n  }\n}\nfunction DashboardComponent_div_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 102);\n    i0.ɵɵelement(1, \"div\", 103);\n    i0.ɵɵelementStart(2, \"div\", 104)(3, \"h6\", 105);\n    i0.ɵɵelement(4, \"i\", 106);\n    i0.ɵɵtext(5, \" User Management \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 107)(7, \"table\", 108)(8, \"thead\", 109)(9, \"tr\")(10, \"th\", 110);\n    i0.ɵɵtext(11, \" Name \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"th\", 111);\n    i0.ɵɵtext(13, \" Email \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"th\", 112);\n    i0.ɵɵtext(15, \" Verified \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"th\", 112);\n    i0.ɵɵtext(17, \" Status \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"th\", 113);\n    i0.ɵɵtext(19, \" Role \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"th\", 114);\n    i0.ɵɵtext(21, \" Activate \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"th\", 114);\n    i0.ɵɵtext(23, \" Delete \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"th\", 114);\n    i0.ɵɵtext(25, \" Details \");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(26, \"tbody\", 115);\n    i0.ɵɵtemplate(27, DashboardComponent_div_29_tr_27_Template, 41, 19, \"tr\", 116);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(27);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r5.filteredUsers);\n  }\n}\nfunction DashboardComponent_div_30_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r25 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 151)(1, \"button\", 152);\n    i0.ɵɵlistener(\"click\", function DashboardComponent_div_30_div_13_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r25);\n      const ctx_r24 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r24.clearSearch());\n    });\n    i0.ɵɵelement(2, \"div\", 153);\n    i0.ɵɵelementStart(3, \"div\", 154);\n    i0.ɵɵelement(4, \"i\", 155);\n    i0.ɵɵtext(5, \" Clear search \");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction DashboardComponent_div_30_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 102);\n    i0.ɵɵelement(1, \"div\", 103);\n    i0.ɵɵelementStart(2, \"div\", 141)(3, \"div\", 142);\n    i0.ɵɵelement(4, \"div\", 143);\n    i0.ɵɵelementStart(5, \"div\", 144);\n    i0.ɵɵelement(6, \"i\", 145);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(7, \"div\", 146)(8, \"div\", 147);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"h3\", 148);\n    i0.ɵɵtext(10, \" No users found \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"p\", 149);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(13, DashboardComponent_div_30_div_13_Template, 6, 0, \"div\", 150);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(12);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r6.searchTerm ? \"No users match your search criteria.\" : \"There are no users in the system yet.\", \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r6.searchTerm);\n  }\n}\nfunction DashboardComponent_div_31_i_51_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 161);\n  }\n}\nfunction DashboardComponent_div_31_i_52_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 188);\n  }\n}\nfunction DashboardComponent_div_31_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r30 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 156);\n    i0.ɵɵlistener(\"click\", function DashboardComponent_div_31_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r30);\n      const ctx_r29 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r29.closeCreateUserModal());\n    });\n    i0.ɵɵelementStart(1, \"div\", 157);\n    i0.ɵɵlistener(\"click\", function DashboardComponent_div_31_Template_div_click_1_listener($event) {\n      return $event.stopPropagation();\n    });\n    i0.ɵɵelementStart(2, \"div\", 158);\n    i0.ɵɵelement(3, \"div\", 38);\n    i0.ɵɵelementStart(4, \"div\", 159)(5, \"h3\", 160);\n    i0.ɵɵelement(6, \"i\", 161);\n    i0.ɵɵtext(7, \" Create New User \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"button\", 162);\n    i0.ɵɵlistener(\"click\", function DashboardComponent_div_31_Template_button_click_8_listener() {\n      i0.ɵɵrestoreView(_r30);\n      const ctx_r32 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r32.closeCreateUserModal());\n    });\n    i0.ɵɵelement(9, \"i\", 163);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(10, \"div\", 40)(11, \"form\", 164, 165);\n    i0.ɵɵlistener(\"ngSubmit\", function DashboardComponent_div_31_Template_form_ngSubmit_11_listener() {\n      i0.ɵɵrestoreView(_r30);\n      const _r26 = i0.ɵɵreference(12);\n      const ctx_r33 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r33.onCreateUser(_r26));\n    });\n    i0.ɵɵelementStart(13, \"div\", 166)(14, \"label\", 167);\n    i0.ɵɵtext(15, \" Full Name \");\n    i0.ɵɵelementStart(16, \"span\", 168);\n    i0.ɵɵtext(17, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(18, \"input\", 169);\n    i0.ɵɵlistener(\"ngModelChange\", function DashboardComponent_div_31_Template_input_ngModelChange_18_listener($event) {\n      i0.ɵɵrestoreView(_r30);\n      const ctx_r34 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r34.newUser.fullName = $event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(19, \"div\", 166)(20, \"label\", 170);\n    i0.ɵɵtext(21, \" Email Address \");\n    i0.ɵɵelementStart(22, \"span\", 168);\n    i0.ɵɵtext(23, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(24, \"input\", 171);\n    i0.ɵɵlistener(\"ngModelChange\", function DashboardComponent_div_31_Template_input_ngModelChange_24_listener($event) {\n      i0.ɵɵrestoreView(_r30);\n      const ctx_r35 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r35.newUser.email = $event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(25, \"div\", 166)(26, \"label\", 172);\n    i0.ɵɵtext(27, \" Role \");\n    i0.ɵɵelementStart(28, \"span\", 168);\n    i0.ɵɵtext(29, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(30, \"select\", 173);\n    i0.ɵɵlistener(\"ngModelChange\", function DashboardComponent_div_31_Template_select_ngModelChange_30_listener($event) {\n      i0.ɵɵrestoreView(_r30);\n      const ctx_r36 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r36.newUser.role = $event);\n    });\n    i0.ɵɵelementStart(31, \"option\", 174);\n    i0.ɵɵtext(32, \"Student\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(33, \"option\", 175);\n    i0.ɵɵtext(34, \"Teacher\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(35, \"option\", 176);\n    i0.ɵɵtext(36, \"Admin\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(37, \"div\", 177)(38, \"div\", 19);\n    i0.ɵɵelement(39, \"i\", 178);\n    i0.ɵɵelementStart(40, \"div\")(41, \"h4\", 179);\n    i0.ɵɵtext(42, \" Password Generation \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(43, \"p\", 180);\n    i0.ɵɵtext(44, \" A secure password will be automatically generated and sent to the user's email address along with login instructions. \");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(45, \"div\", 181)(46, \"button\", 182);\n    i0.ɵɵlistener(\"click\", function DashboardComponent_div_31_Template_button_click_46_listener() {\n      i0.ɵɵrestoreView(_r30);\n      const ctx_r37 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r37.closeCreateUserModal());\n    });\n    i0.ɵɵtext(47, \" Cancel \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(48, \"button\", 183);\n    i0.ɵɵelement(49, \"div\", 184);\n    i0.ɵɵelementStart(50, \"div\", 185);\n    i0.ɵɵtemplate(51, DashboardComponent_div_31_i_51_Template, 1, 0, \"i\", 186);\n    i0.ɵɵtemplate(52, DashboardComponent_div_31_i_52_Template, 1, 0, \"i\", 187);\n    i0.ɵɵtext(53);\n    i0.ɵɵelementEnd()()()()()()();\n  }\n  if (rf & 2) {\n    const _r26 = i0.ɵɵreference(12);\n    const ctx_r7 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(18);\n    i0.ɵɵproperty(\"ngModel\", ctx_r7.newUser.fullName);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngModel\", ctx_r7.newUser.email);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngModel\", ctx_r7.newUser.role);\n    i0.ɵɵadvance(18);\n    i0.ɵɵclassProp(\"opacity-50\", !_r26.valid || ctx_r7.creatingUser);\n    i0.ɵɵproperty(\"disabled\", !_r26.valid || ctx_r7.creatingUser);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r7.creatingUser);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r7.creatingUser);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r7.creatingUser ? \"Creating...\" : \"Create User\", \" \");\n  }\n}\nexport class DashboardComponent {\n  constructor(authService, router, toastService) {\n    this.authService = authService;\n    this.router = router;\n    this.toastService = toastService;\n    this.users = [];\n    this.error = '';\n    this.message = '';\n    this.roles = ['student', 'teacher', 'admin'];\n    this.loading = true;\n    this.currentUser = null;\n    this.searchTerm = '';\n    this.filteredUsers = [];\n    // Create user modal properties\n    this.showCreateUserModal = false;\n    this.creatingUser = false;\n    this.newUser = {\n      fullName: '',\n      email: '',\n      role: 'student'\n    };\n  }\n  ngOnInit() {\n    this.loadUserData();\n  }\n  loadUserData() {\n    this.loading = true;\n    const token = localStorage.getItem('token');\n    const userStr = localStorage.getItem('user');\n    if (!token || !userStr) {\n      this.router.navigate(['/admin/login']);\n      return;\n    }\n    this.currentUser = JSON.parse(userStr);\n    // Check if user is admin\n    if (this.currentUser.role !== 'admin') {\n      this.router.navigate(['/']);\n      return;\n    }\n    this.authService.getAllUsers(token).subscribe({\n      next: res => {\n        this.users = res;\n        this.filteredUsers = [...this.users];\n        this.loading = false;\n      },\n      error: err => {\n        this.error = err.error?.message || 'Failed to fetch users';\n        this.loading = false;\n      }\n    });\n  }\n  searchUsers() {\n    if (!this.searchTerm.trim()) {\n      this.filteredUsers = [...this.users];\n      return;\n    }\n    const term = this.searchTerm.toLowerCase().trim();\n    this.filteredUsers = this.users.filter(user => user.fullName.toLowerCase().includes(term) || user.email.toLowerCase().includes(term) || user.role.toLowerCase().includes(term));\n  }\n  clearSearch() {\n    this.searchTerm = '';\n    this.filteredUsers = [...this.users];\n  }\n  onRoleChange(userId, newRole) {\n    const token = localStorage.getItem('token');\n    this.authService.updateUserRole(userId, newRole, token).subscribe({\n      next: res => {\n        this.message = res.message;\n        this.error = '';\n        // Update the user in the local arrays\n        const userIndex = this.users.findIndex(u => u._id === userId);\n        if (userIndex !== -1) {\n          this.users[userIndex].role = newRole;\n        }\n        const filteredIndex = this.filteredUsers.findIndex(u => u._id === userId);\n        if (filteredIndex !== -1) {\n          this.filteredUsers[filteredIndex].role = newRole;\n        }\n        // Auto-hide message after 3 seconds\n        setTimeout(() => {\n          this.message = '';\n        }, 3000);\n      },\n      error: err => {\n        this.error = err.error?.message || 'Failed to update role';\n        this.message = '';\n        // Auto-hide error after 3 seconds\n        setTimeout(() => {\n          this.error = '';\n        }, 3000);\n      }\n    });\n  }\n  onDeleteUser(userId) {\n    const confirmDelete = confirm('Are you sure you want to delete this user?');\n    if (!confirmDelete) return;\n    const token = localStorage.getItem('token');\n    this.authService.deleteUser(userId, token).subscribe({\n      next: res => {\n        this.message = res.message;\n        this.error = '';\n        // Remove user from both arrays\n        this.users = this.users.filter(u => u._id !== userId);\n        this.filteredUsers = this.filteredUsers.filter(u => u._id !== userId);\n        // Auto-hide message after 3 seconds\n        setTimeout(() => {\n          this.message = '';\n        }, 3000);\n      },\n      error: err => {\n        this.error = err.error?.message || 'Failed to delete user';\n        this.message = '';\n        // Auto-hide error after 3 seconds\n        setTimeout(() => {\n          this.error = '';\n        }, 3000);\n      }\n    });\n  }\n  toggleUserActivation(userId, currentStatus) {\n    const newStatus = !currentStatus;\n    const action = newStatus ? 'activate' : 'deactivate';\n    // Find the user to get their name for better messaging\n    const user = this.users.find(u => u._id === userId);\n    const userName = user?.fullName || user?.firstName || 'User';\n    const confirmAction = confirm(`Are you sure you want to ${action} ${userName}?`);\n    if (!confirmAction) return;\n    const token = localStorage.getItem('token');\n    this.authService.toggleUserActivation(userId, newStatus, token).subscribe({\n      next: res => {\n        const statusText = newStatus ? 'activated' : 'deactivated';\n        const successMessage = `${userName} has been ${statusText} successfully`;\n        // Show success toast\n        this.toastService.showSuccess(successMessage);\n        // Clear any existing messages\n        this.message = '';\n        this.error = '';\n        // Update user in both arrays\n        const userIndex = this.users.findIndex(u => u._id === userId);\n        if (userIndex !== -1) {\n          this.users[userIndex].isActive = newStatus;\n        }\n        const filteredIndex = this.filteredUsers.findIndex(u => u._id === userId);\n        if (filteredIndex !== -1) {\n          this.filteredUsers[filteredIndex].isActive = newStatus;\n        }\n        // Apply filters to refresh the view\n        this.applyFilters();\n      },\n      error: err => {\n        const statusText = newStatus ? 'activate' : 'deactivate';\n        const errorMessage = err.error?.message || `Failed to ${statusText} ${userName}`;\n        // Show error toast\n        this.toastService.showError(errorMessage);\n        // Clear any existing messages\n        this.message = '';\n        this.error = '';\n      }\n    });\n  }\n  getStudentCount() {\n    return this.users.filter(u => u.role === 'student').length;\n  }\n  getTeacherCount() {\n    return this.users.filter(u => u.role === 'teacher').length;\n  }\n  getAdminCount() {\n    return this.users.filter(u => u.role === 'admin').length;\n  }\n  getActiveCount() {\n    return this.users.filter(u => u.isActive !== false).length;\n  }\n  getInactiveCount() {\n    return this.users.filter(u => u.isActive === false).length;\n  }\n  logout() {\n    this.authService.logout();\n    this.router.navigate(['/admin/login']);\n  }\n  showUserDetails(userId) {\n    this.router.navigate(['/admin/userdetails', userId]);\n  }\n  applyFilters() {\n    this.searchUsers();\n  }\n  // Create user modal methods\n  openCreateUserModal() {\n    this.showCreateUserModal = true;\n    this.resetNewUserForm();\n  }\n  closeCreateUserModal() {\n    this.showCreateUserModal = false;\n    this.resetNewUserForm();\n  }\n  resetNewUserForm() {\n    this.newUser = {\n      fullName: '',\n      email: '',\n      role: 'student'\n    };\n    this.creatingUser = false;\n  }\n  onCreateUser(form) {\n    if (form.invalid || this.creatingUser) {\n      return;\n    }\n    this.creatingUser = true;\n    this.error = '';\n    this.message = '';\n    const token = localStorage.getItem('token');\n    this.authService.createUser(this.newUser, token).subscribe({\n      next: res => {\n        // Show success message\n        this.message = res.message;\n        this.error = '';\n        // Add new user to the lists\n        this.users.push(res.user);\n        this.filteredUsers = [...this.users];\n        this.applyFilters();\n        // Show success toast with credentials info\n        this.toastService.showSuccess(`User created successfully! Login credentials sent to ${res.user.email}`);\n        // Close modal and reset form\n        this.closeCreateUserModal();\n        // Auto-hide message after 5 seconds\n        setTimeout(() => {\n          this.message = '';\n        }, 5000);\n      },\n      error: err => {\n        this.error = err.error?.message || 'Failed to create user';\n        this.message = '';\n        this.creatingUser = false;\n        // Show error toast\n        this.toastService.showError(this.error);\n        // Auto-hide error after 3 seconds\n        setTimeout(() => {\n          this.error = '';\n        }, 3000);\n      }\n    });\n  }\n  static {\n    this.ɵfac = function DashboardComponent_Factory(t) {\n      return new (t || DashboardComponent)(i0.ɵɵdirectiveInject(i1.AuthService), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i3.ToastService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: DashboardComponent,\n      selectors: [[\"app-dashboard\"]],\n      decls: 32,\n      vars: 8,\n      consts: [[1, \"container-fluid\", \"p-4\", \"md:p-6\", \"bg-[#edf1f4]\", \"dark:bg-[#121212]\", \"min-h-screen\", \"relative\"], [1, \"absolute\", \"inset-0\", \"overflow-hidden\", \"pointer-events-none\"], [1, \"absolute\", \"top-[15%]\", \"left-[10%]\", \"w-64\", \"h-64\", \"rounded-full\", \"bg-gradient-to-br\", \"from-[#4f5fad]/5\", \"to-transparent\", \"dark:from-[#6d78c9]/3\", \"dark:to-transparent\", \"blur-3xl\"], [1, \"absolute\", \"bottom-[20%]\", \"right-[10%]\", \"w-80\", \"h-80\", \"rounded-full\", \"bg-gradient-to-tl\", \"from-[#4f5fad]/5\", \"to-transparent\", \"dark:from-[#6d78c9]/3\", \"dark:to-transparent\", \"blur-3xl\"], [1, \"absolute\", \"inset-0\", \"opacity-5\", \"dark:opacity-[0.03]\"], [1, \"h-full\", \"grid\", \"grid-cols-12\"], [1, \"border-r\", \"border-[#4f5fad]\", \"dark:border-[#6d78c9]\"], [1, \"relative\", \"z-10\"], [1, \"flex\", \"flex-col\", \"md:flex-row\", \"md:items-center\", \"md:justify-between\", \"mb-8\"], [1, \"text-2xl\", \"font-bold\", \"bg-gradient-to-r\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#6d78c9]\", \"dark:to-[#4f5fad]\", \"bg-clip-text\", \"text-transparent\", \"mb-2\"], [1, \"text-sm\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\"], [\"class\", \"bg-[#afcf75]/10 dark:bg-[#afcf75]/5 border border-[#afcf75] dark:border-[#afcf75]/30 rounded-lg p-4 mx-auto max-w-3xl mb-6 backdrop-blur-sm\", 4, \"ngIf\"], [\"class\", \"bg-[#ff6b69]/10 dark:bg-[#ff6b69]/5 border border-[#ff6b69] dark:border-[#ff6b69]/30 rounded-lg p-4 mx-auto max-w-3xl mb-6 backdrop-blur-sm\", 4, \"ngIf\"], [\"class\", \"flex justify-center items-center py-20\", 4, \"ngIf\"], [\"class\", \"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8\", 4, \"ngIf\"], [\"class\", \"flex flex-col md:flex-row md:items-center md:justify-between mb-6 gap-4\", 4, \"ngIf\"], [\"class\", \"bg-white dark:bg-[#1e1e1e] rounded-xl shadow-md dark:shadow-[0_4px_20px_rgba(0,0,0,0.2)] overflow-hidden mb-8 backdrop-blur-sm border border-[#edf1f4]/50 dark:border-[#2a2a2a] relative\", 4, \"ngIf\"], [\"class\", \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\", 3, \"click\", 4, \"ngIf\"], [1, \"bg-[#afcf75]/10\", \"dark:bg-[#afcf75]/5\", \"border\", \"border-[#afcf75]\", \"dark:border-[#afcf75]/30\", \"rounded-lg\", \"p-4\", \"mx-auto\", \"max-w-3xl\", \"mb-6\", \"backdrop-blur-sm\"], [1, \"flex\", \"items-start\"], [1, \"text-[#2a5a03]\", \"dark:text-[#afcf75]\", \"mr-3\", \"text-xl\", \"relative\"], [1, \"fas\", \"fa-check-circle\"], [1, \"absolute\", \"inset-0\", \"bg-[#afcf75]/20\", \"dark:bg-[#afcf75]/20\", \"blur-xl\", \"rounded-full\", \"transform\", \"scale-150\", \"-z-10\"], [1, \"flex-1\"], [1, \"font-medium\", \"text-[#2a5a03]\", \"dark:text-[#afcf75]\", \"mb-1\"], [1, \"text-sm\", \"text-[#2a5a03]/80\", \"dark:text-[#afcf75]/80\"], [1, \"bg-[#ff6b69]/10\", \"dark:bg-[#ff6b69]/5\", \"border\", \"border-[#ff6b69]\", \"dark:border-[#ff6b69]/30\", \"rounded-lg\", \"p-4\", \"mx-auto\", \"max-w-3xl\", \"mb-6\", \"backdrop-blur-sm\"], [1, \"text-[#ff6b69]\", \"dark:text-[#ff8785]\", \"mr-3\", \"text-xl\", \"relative\"], [1, \"fas\", \"fa-exclamation-triangle\"], [1, \"absolute\", \"inset-0\", \"bg-[#ff6b69]/20\", \"dark:bg-[#ff8785]/20\", \"blur-xl\", \"rounded-full\", \"transform\", \"scale-150\", \"-z-10\"], [1, \"font-medium\", \"text-[#ff6b69]\", \"dark:text-[#ff8785]\", \"mb-1\"], [1, \"text-sm\", \"text-[#ff6b69]/80\", \"dark:text-[#ff8785]/80\"], [1, \"flex\", \"justify-center\", \"items-center\", \"py-20\"], [1, \"relative\"], [1, \"w-14\", \"h-14\", \"border-4\", \"border-[#4f5fad]/20\", \"dark:border-[#6d78c9]/20\", \"border-t-[#4f5fad]\", \"dark:border-t-[#6d78c9]\", \"rounded-full\", \"animate-spin\"], [1, \"absolute\", \"inset-0\", \"bg-[#4f5fad]/20\", \"dark:bg-[#6d78c9]/20\", \"blur-xl\", \"rounded-full\", \"transform\", \"scale-150\", \"-z-10\"], [1, \"grid\", \"grid-cols-1\", \"md:grid-cols-3\", \"gap-6\", \"mb-8\"], [1, \"bg-white\", \"dark:bg-[#1e1e1e]\", \"rounded-xl\", \"shadow-md\", \"dark:shadow-[0_4px_20px_rgba(0,0,0,0.2)]\", \"overflow-hidden\", \"backdrop-blur-sm\", \"border\", \"border-[#edf1f4]/50\", \"dark:border-[#2a2a2a]\", \"relative\", \"group\", \"hover:shadow-lg\", \"dark:hover:shadow-[0_8px_30px_rgba(0,0,0,0.3)]\", \"transition-all\", \"duration-300\", \"hover:-translate-y-1\"], [1, \"absolute\", \"top-0\", \"left-0\", \"right-0\", \"h-1\", \"bg-gradient-to-r\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#6d78c9]\", \"dark:to-[#4f5fad]\"], [1, \"absolute\", \"top-0\", \"left-0\", \"right-0\", \"h-1\", \"bg-gradient-to-r\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#6d78c9]\", \"dark:to-[#4f5fad]\", \"opacity-0\", \"group-hover:opacity-100\", \"blur-md\", \"transition-opacity\", \"duration-300\"], [1, \"p-6\"], [1, \"flex\", \"items-center\"], [1, \"text-sm\", \"font-medium\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"uppercase\", \"tracking-wider\"], [1, \"mt-1\", \"text-2xl\", \"font-semibold\", \"bg-gradient-to-r\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#6d78c9]\", \"dark:to-[#4f5fad]\", \"bg-clip-text\", \"text-transparent\"], [1, \"ml-4\", \"relative\"], [1, \"absolute\", \"inset-0\", \"bg-[#4f5fad]/10\", \"dark:bg-[#6d78c9]/10\", \"rounded-full\", \"blur-xl\", \"opacity-0\", \"group-hover:opacity-100\", \"transition-opacity\", \"duration-300\", \"scale-150\"], [1, \"relative\", \"z-10\", \"bg-[#edf1f4]/80\", \"dark:bg-[#2a2a2a]/80\", \"rounded-full\", \"p-2.5\", \"backdrop-blur-sm\", \"group-hover:scale-110\", \"transition-transform\", \"duration-300\"], [1, \"fas\", \"fa-users\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"text-xl\"], [1, \"mt-4\", \"w-full\", \"bg-[#edf1f4]\", \"dark:bg-[#2a2a2a]\", \"rounded-full\", \"h-2.5\", \"overflow-hidden\", \"relative\"], [1, \"absolute\", \"inset-0\", \"bg-gradient-to-r\", \"from-[#3d4a85]/10\", \"to-[#4f5fad]/10\", \"dark:from-[#6d78c9]/10\", \"dark:to-[#4f5fad]/10\", \"opacity-0\", \"group-hover:opacity-100\", \"transition-opacity\", \"duration-300\", \"blur-md\"], [1, \"bg-gradient-to-r\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#6d78c9]\", \"dark:to-[#4f5fad]\", \"h-2.5\", \"rounded-full\", \"relative\", \"z-10\", 2, \"width\", \"100%\"], [1, \"absolute\", \"inset-0\", \"bg-[#00f7ff]/20\", \"rounded-full\", \"animate-pulse\"], [1, \"absolute\", \"top-0\", \"left-0\", \"right-0\", \"h-1\", \"bg-gradient-to-r\", \"from-[#2a5a03]\", \"to-[#afcf75]\", \"dark:from-[#2a5a03]\", \"dark:to-[#afcf75]\"], [1, \"absolute\", \"top-0\", \"left-0\", \"right-0\", \"h-1\", \"bg-gradient-to-r\", \"from-[#2a5a03]\", \"to-[#afcf75]\", \"dark:from-[#2a5a03]\", \"dark:to-[#afcf75]\", \"opacity-0\", \"group-hover:opacity-100\", \"blur-md\", \"transition-opacity\", \"duration-300\"], [1, \"mt-1\", \"flex\", \"space-x-4\", \"text-sm\"], [1, \"w-2\", \"h-2\", \"rounded-full\", \"bg-[#afcf75]\", \"dark:bg-[#afcf75]\", \"mr-1.5\", \"relative\"], [1, \"absolute\", \"inset-0\", \"bg-[#afcf75]\", \"rounded-full\", \"animate-ping\", \"opacity-75\"], [1, \"text-[#2a5a03]\", \"dark:text-[#afcf75]\", \"font-medium\"], [1, \"w-2\", \"h-2\", \"rounded-full\", \"bg-[#ff6b69]\", \"dark:bg-[#ff8785]\", \"mr-1.5\"], [1, \"text-[#ff6b69]\", \"dark:text-[#ff8785]\", \"font-medium\"], [1, \"absolute\", \"inset-0\", \"bg-[#afcf75]/10\", \"dark:bg-[#afcf75]/10\", \"rounded-full\", \"blur-xl\", \"opacity-0\", \"group-hover:opacity-100\", \"transition-opacity\", \"duration-300\", \"scale-150\"], [1, \"fas\", \"fa-check-circle\", \"text-[#afcf75]\", \"dark:text-[#afcf75]\", \"text-xl\"], [1, \"absolute\", \"inset-0\", \"bg-gradient-to-r\", \"from-[#2a5a03]/10\", \"to-[#afcf75]/10\", \"dark:from-[#2a5a03]/10\", \"dark:to-[#afcf75]/10\", \"opacity-0\", \"group-hover:opacity-100\", \"transition-opacity\", \"duration-300\", \"blur-md\"], [1, \"flex\", \"h-full\", \"relative\", \"z-10\"], [1, \"bg-gradient-to-r\", \"from-[#2a5a03]\", \"to-[#afcf75]\", \"dark:from-[#2a5a03]\", \"dark:to-[#afcf75]\", \"h-2.5\", \"relative\"], [1, \"absolute\", \"inset-0\", \"bg-[#afcf75]/20\", \"rounded-full\", \"animate-pulse\"], [1, \"bg-gradient-to-r\", \"from-[#ff6b69]\", \"to-[#ff8785]\", \"dark:from-[#ff6b69]\", \"dark:to-[#ff8785]\", \"h-2.5\", \"relative\"], [1, \"absolute\", \"inset-0\", \"bg-[#ff6b69]/20\", \"rounded-full\", \"animate-pulse\"], [1, \"absolute\", \"top-0\", \"left-0\", \"right-0\", \"h-1\", \"bg-gradient-to-r\", \"from-[#4a89ce]\", \"to-[#7826b5]\", \"dark:from-[#4a89ce]\", \"dark:to-[#7826b5]\"], [1, \"absolute\", \"top-0\", \"left-0\", \"right-0\", \"h-1\", \"bg-gradient-to-r\", \"from-[#4a89ce]\", \"to-[#7826b5]\", \"dark:from-[#4a89ce]\", \"dark:to-[#7826b5]\", \"opacity-0\", \"group-hover:opacity-100\", \"blur-md\", \"transition-opacity\", \"duration-300\"], [1, \"mt-1\", \"flex\", \"flex-wrap\", \"gap-4\", \"text-sm\"], [1, \"w-2\", \"h-2\", \"rounded-full\", \"bg-[#4a89ce]\", \"dark:bg-[#4a89ce]\", \"mr-1.5\"], [1, \"text-[#4a89ce]\", \"dark:text-[#4a89ce]\", \"font-medium\"], [1, \"w-2\", \"h-2\", \"rounded-full\", \"bg-[#7826b5]\", \"dark:bg-[#7826b5]\", \"mr-1.5\"], [1, \"text-[#7826b5]\", \"dark:text-[#7826b5]\", \"font-medium\"], [1, \"w-2\", \"h-2\", \"rounded-full\", \"bg-[#4f5fad]\", \"dark:bg-[#6d78c9]\", \"mr-1.5\"], [1, \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"font-medium\"], [1, \"absolute\", \"inset-0\", \"bg-[#4a89ce]/10\", \"dark:bg-[#4a89ce]/10\", \"rounded-full\", \"blur-xl\", \"opacity-0\", \"group-hover:opacity-100\", \"transition-opacity\", \"duration-300\", \"scale-150\"], [1, \"fas\", \"fa-user-tag\", \"text-[#4a89ce]\", \"dark:text-[#4a89ce]\", \"text-xl\"], [1, \"absolute\", \"inset-0\", \"bg-gradient-to-r\", \"from-[#4a89ce]/10\", \"to-[#7826b5]/10\", \"dark:from-[#4a89ce]/10\", \"dark:to-[#7826b5]/10\", \"opacity-0\", \"group-hover:opacity-100\", \"transition-opacity\", \"duration-300\", \"blur-md\"], [1, \"bg-gradient-to-r\", \"from-[#4a89ce]\", \"to-[#4a89ce]\", \"dark:from-[#4a89ce]\", \"dark:to-[#4a89ce]\", \"h-2.5\", \"relative\"], [1, \"absolute\", \"inset-0\", \"bg-[#4a89ce]/20\", \"rounded-full\", \"animate-pulse\"], [1, \"bg-gradient-to-r\", \"from-[#7826b5]\", \"to-[#7826b5]\", \"dark:from-[#7826b5]\", \"dark:to-[#7826b5]\", \"h-2.5\", \"relative\"], [1, \"absolute\", \"inset-0\", \"bg-[#7826b5]/20\", \"rounded-full\", \"animate-pulse\"], [1, \"bg-gradient-to-r\", \"from-[#4f5fad]\", \"to-[#4f5fad]\", \"dark:from-[#6d78c9]\", \"dark:to-[#6d78c9]\", \"h-2.5\", \"relative\"], [1, \"absolute\", \"inset-0\", \"bg-[#4f5fad]/20\", \"dark:bg-[#6d78c9]/20\", \"rounded-full\", \"animate-pulse\"], [1, \"flex\", \"justify-between\", \"mt-2\", \"text-xs\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\"], [1, \"flex\", \"flex-col\", \"md:flex-row\", \"md:items-center\", \"md:justify-between\", \"mb-6\", \"gap-4\"], [1, \"inline-flex\", \"items-center\", \"px-4\", \"py-2.5\", \"text-sm\", \"font-medium\", \"rounded-lg\", \"relative\", \"overflow-hidden\", \"group\", \"transition-all\", \"duration-300\", \"hover:shadow-lg\", \"hover:-translate-y-0.5\", \"w-fit\", 3, \"click\"], [1, \"absolute\", \"inset-0\", \"bg-gradient-to-r\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#4f5fad]\", \"dark:to-[#6d78c9]\", \"rounded-lg\", \"opacity-90\", \"group-hover:opacity-100\", \"transition-opacity\"], [1, \"absolute\", \"inset-0\", \"bg-gradient-to-r\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#4f5fad]\", \"dark:to-[#6d78c9]\", \"rounded-lg\", \"opacity-0\", \"group-hover:opacity-30\", \"blur-xl\", \"transition-opacity\"], [1, \"relative\", \"z-10\", \"flex\", \"items-center\", \"text-white\"], [1, \"fas\", \"fa-user-plus\", \"mr-2\", \"group-hover:scale-110\", \"transition-transform\"], [1, \"relative\", \"w-full\", \"md:w-80\", \"group\"], [1, \"absolute\", \"inset-y-0\", \"left-0\", \"pl-3\", \"flex\", \"items-center\", \"pointer-events-none\"], [1, \"fas\", \"fa-search\", \"text-[#bdc6cc]\", \"dark:text-[#6d6870]\", \"group-focus-within:text-[#4f5fad]\", \"dark:group-focus-within:text-[#6d78c9]\", \"transition-colors\"], [\"type\", \"text\", \"placeholder\", \"Search users by name, email, or role...\", 1, \"w-full\", \"pl-10\", \"pr-10\", \"py-2.5\", \"text-sm\", \"rounded-lg\", \"border\", \"border-[#bdc6cc]\", \"dark:border-[#2a2a2a]\", \"bg-white\", \"dark:bg-[#1e1e1e]\", \"text-[#6d6870]\", \"dark:text-[#e0e0e0]\", \"focus:outline-none\", \"focus:border-[#4f5fad]\", \"dark:focus:border-[#6d78c9]\", \"focus:ring-2\", \"focus:ring-[#4f5fad]/20\", \"dark:focus:ring-[#6d78c9]/20\", \"transition-all\", 3, \"value\", \"input\"], [1, \"absolute\", \"inset-y-0\", \"left-0\", \"pl-3\", \"flex\", \"items-center\", \"pointer-events-none\", \"opacity-0\", \"group-focus-within:opacity-100\", \"transition-opacity\"], [1, \"w-0.5\", \"h-4\", \"bg-gradient-to-b\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#6d78c9]\", \"dark:to-[#4f5fad]\", \"rounded-full\"], [\"class\", \"absolute inset-y-0 right-0 pr-3 flex items-center text-[#bdc6cc] dark:text-[#6d6870] hover:text-[#ff6b69] dark:hover:text-[#ff8785] transition-colors\", 3, \"click\", 4, \"ngIf\"], [1, \"absolute\", \"inset-y-0\", \"right-0\", \"pr-3\", \"flex\", \"items-center\", \"text-[#bdc6cc]\", \"dark:text-[#6d6870]\", \"hover:text-[#ff6b69]\", \"dark:hover:text-[#ff8785]\", \"transition-colors\", 3, \"click\"], [1, \"fas\", \"fa-times-circle\"], [1, \"bg-white\", \"dark:bg-[#1e1e1e]\", \"rounded-xl\", \"shadow-md\", \"dark:shadow-[0_4px_20px_rgba(0,0,0,0.2)]\", \"overflow-hidden\", \"mb-8\", \"backdrop-blur-sm\", \"border\", \"border-[#edf1f4]/50\", \"dark:border-[#2a2a2a]\", \"relative\"], [1, \"absolute\", \"top-0\", \"left-0\", \"right-0\", \"h-0.5\", \"bg-gradient-to-r\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#6d78c9]\", \"dark:to-[#4f5fad]\"], [1, \"p-5\", \"border-b\", \"border-[#edf1f4]/50\", \"dark:border-[#2a2a2a]\", \"flex\", \"items-center\", \"justify-between\"], [1, \"font-bold\", \"bg-gradient-to-r\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#6d78c9]\", \"dark:to-[#4f5fad]\", \"bg-clip-text\", \"text-transparent\", \"flex\", \"items-center\"], [1, \"fas\", \"fa-users-cog\", \"mr-2\"], [1, \"overflow-x-auto\"], [1, \"w-full\", \"divide-y\", \"divide-[#edf1f4]\", \"dark:divide-[#2a2a2a]\", \"table-fixed\"], [1, \"bg-[#f8fafc]\", \"dark:bg-[#1a1a1a]\"], [\"scope\", \"col\", 1, \"px-3\", \"py-3\", \"text-left\", \"text-xs\", \"font-medium\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"uppercase\", \"tracking-wider\", \"w-[15%]\"], [\"scope\", \"col\", 1, \"px-3\", \"py-3\", \"text-left\", \"text-xs\", \"font-medium\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"uppercase\", \"tracking-wider\", \"w-[20%]\"], [\"scope\", \"col\", 1, \"px-2\", \"py-3\", \"text-center\", \"text-xs\", \"font-medium\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"uppercase\", \"tracking-wider\", \"w-[8%]\"], [\"scope\", \"col\", 1, \"px-2\", \"py-3\", \"text-center\", \"text-xs\", \"font-medium\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"uppercase\", \"tracking-wider\", \"w-[10%]\"], [\"scope\", \"col\", 1, \"px-2\", \"py-3\", \"text-center\", \"text-xs\", \"font-medium\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"uppercase\", \"tracking-wider\", \"w-[13%]\"], [1, \"bg-white\", \"dark:bg-[#1e1e1e]\", \"divide-y\", \"divide-[#edf1f4]\", \"dark:divide-[#2a2a2a]\"], [\"class\", \"hover:bg-[#f8fafc] dark:hover:bg-[#1a1a1a] transition-colors\", 4, \"ngFor\", \"ngForOf\"], [1, \"hover:bg-[#f8fafc]\", \"dark:hover:bg-[#1a1a1a]\", \"transition-colors\"], [1, \"px-3\", \"py-3\", \"whitespace-nowrap\", \"truncate\"], [1, \"flex-shrink-0\", \"h-8\", \"w-8\", \"rounded-full\", \"bg-gradient-to-r\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#6d78c9]\", \"dark:to-[#4f5fad]\", \"text-white\", \"flex\", \"items-center\", \"justify-center\", \"text-xs\", \"shadow-md\", \"relative\", \"group\"], [1, \"absolute\", \"inset-0\", \"bg-[#4f5fad]/20\", \"dark:bg-[#6d78c9]/20\", \"rounded-full\", \"blur-md\", \"opacity-0\", \"group-hover:opacity-100\", \"transition-opacity\"], [1, \"ml-3\"], [1, \"text-sm\", \"font-medium\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"truncate\", \"max-w-[120px]\"], [1, \"px-3\", \"py-3\", \"whitespace-nowrap\"], [1, \"text-sm\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"truncate\", \"max-w-[150px]\"], [1, \"px-2\", \"py-3\", \"whitespace-nowrap\", \"text-center\"], [1, \"px-2\", \"py-1\", \"text-xs\", \"rounded-lg\", \"inline-flex\", \"items-center\", \"justify-center\", 3, \"ngClass\"], [1, \"px-3\", \"py-1.5\", \"text-xs\", \"rounded-full\", \"inline-flex\", \"items-center\", \"justify-center\", \"font-semibold\", \"transition-all\", \"duration-300\", 3, \"ngClass\"], [1, \"relative\", \"group\"], [1, \"w-full\", \"px-2\", \"py-1.5\", \"text-xs\", \"rounded-lg\", \"border\", \"border-[#bdc6cc]\", \"dark:border-[#2a2a2a]\", \"bg-white\", \"dark:bg-[#1a1a1a]\", \"text-[#6d6870]\", \"dark:text-[#e0e0e0]\", \"focus:outline-none\", \"focus:border-[#4f5fad]\", \"dark:focus:border-[#6d78c9]\", \"focus:ring-1\", \"focus:ring-[#4f5fad]/20\", \"dark:focus:ring-[#6d78c9]/20\", \"transition-all\", \"appearance-none\", \"pr-7\", 3, \"value\", \"change\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [1, \"absolute\", \"inset-y-0\", \"right-0\", \"flex\", \"items-center\", \"pr-2\", \"pointer-events-none\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\"], [1, \"fas\", \"fa-chevron-down\", \"text-xs\"], [1, \"px-3\", \"py-2\", \"text-xs\", \"rounded-lg\", \"font-semibold\", \"flex\", \"items-center\", \"justify-center\", \"mx-auto\", \"transition-all\", \"duration-300\", \"w-full\", \"relative\", \"overflow-hidden\", \"group\", \"border\", \"shadow-sm\", \"hover:shadow-md\", \"transform\", \"hover:scale-105\", 3, \"ngClass\", \"title\", \"click\"], [1, \"absolute\", \"inset-0\", \"opacity-0\", \"group-hover:opacity-100\", \"transition-opacity\", \"duration-300\", 3, \"ngClass\"], [1, \"relative\", \"z-10\", \"mr-2\", \"transition-transform\", \"duration-300\", \"group-hover:scale-110\"], [\"title\", \"Supprimer d\\u00E9finitivement cet utilisateur\", 1, \"px-2\", \"py-1.5\", \"text-xs\", \"rounded-lg\", \"bg-[#ff6b69]/10\", \"dark:bg-[#ff6b69]/5\", \"text-[#ff6b69]\", \"dark:text-[#ff8785]\", \"hover:bg-[#ff6b69]/20\", \"dark:hover:bg-[#ff6b69]/10\", \"font-medium\", \"flex\", \"items-center\", \"justify-center\", \"mx-auto\", \"transition-all\", \"w-full\", 3, \"click\"], [1, \"fas\", \"fa-trash-alt\", \"mr-1.5\"], [1, \"px-2\", \"py-1.5\", \"text-xs\", \"rounded-lg\", \"bg-[#4f5fad]/10\", \"dark:bg-[#6d78c9]/5\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"hover:bg-[#4f5fad]/20\", \"dark:hover:bg-[#6d78c9]/10\", \"font-medium\", \"flex\", \"items-center\", \"justify-center\", \"mx-auto\", \"transition-all\", \"w-full\", 3, \"click\"], [1, \"fas\", \"fa-eye\", \"mr-1.5\"], [3, \"value\"], [1, \"p-10\", \"text-center\"], [1, \"relative\", \"mx-auto\", \"w-20\", \"h-20\", \"mb-6\"], [1, \"absolute\", \"inset-0\", \"bg-[#4f5fad]/20\", \"dark:bg-[#6d78c9]/20\", \"rounded-full\", \"blur-xl\"], [1, \"relative\", \"z-10\", \"w-20\", \"h-20\", \"rounded-full\", \"bg-gradient-to-br\", \"from-[#edf1f4]\", \"to-white\", \"dark:from-[#1a1a1a]\", \"dark:to-[#2a2a2a]\", \"flex\", \"items-center\", \"justify-center\", \"shadow-lg\"], [1, \"fas\", \"fa-users\", \"text-3xl\", \"bg-gradient-to-r\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#6d78c9]\", \"dark:to-[#4f5fad]\", \"bg-clip-text\", \"text-transparent\"], [1, \"absolute\", \"inset-0\", \"border-2\", \"border-[#4f5fad]/20\", \"dark:border-[#6d78c9]/20\", \"rounded-full\", \"animate-ping\", \"opacity-75\"], [1, \"absolute\", \"inset-0\", \"border\", \"border-[#4f5fad]/40\", \"dark:border-[#6d78c9]/40\", \"rounded-full\", \"animate-pulse\"], [1, \"text-xl\", \"font-bold\", \"bg-gradient-to-r\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#6d78c9]\", \"dark:to-[#4f5fad]\", \"bg-clip-text\", \"text-transparent\", \"mb-3\"], [1, \"text-sm\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"max-w-md\", \"mx-auto\"], [\"class\", \"mt-8\", 4, \"ngIf\"], [1, \"mt-8\"], [1, \"inline-flex\", \"items-center\", \"px-4\", \"py-2.5\", \"text-sm\", \"relative\", \"overflow-hidden\", \"group\", 3, \"click\"], [1, \"absolute\", \"inset-0\", \"bg-gradient-to-r\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#3d4a85]\", \"dark:to-[#6d78c9]\", \"rounded-lg\", \"opacity-10\", \"dark:opacity-20\", \"group-hover:opacity-20\", \"dark:group-hover:opacity-30\", \"transition-opacity\"], [1, \"relative\", \"z-10\", \"flex\", \"items-center\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"font-medium\"], [1, \"fas\", \"fa-times-circle\", \"mr-2\"], [1, \"fixed\", \"inset-0\", \"bg-black\", \"bg-opacity-50\", \"flex\", \"items-center\", \"justify-center\", \"z-50\", \"p-4\", 3, \"click\"], [1, \"bg-white\", \"dark:bg-[#1e1e1e]\", \"rounded-xl\", \"shadow-2xl\", \"max-w-md\", \"w-full\", \"max-h-[90vh]\", \"overflow-y-auto\", \"relative\", 3, \"click\"], [1, \"p-6\", \"border-b\", \"border-[#edf1f4]\", \"dark:border-[#2a2a2a]\", \"relative\"], [1, \"flex\", \"items-center\", \"justify-between\"], [1, \"text-xl\", \"font-bold\", \"bg-gradient-to-r\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#6d78c9]\", \"dark:to-[#4f5fad]\", \"bg-clip-text\", \"text-transparent\"], [1, \"fas\", \"fa-user-plus\", \"mr-2\"], [1, \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"hover:text-[#ff6b69]\", \"dark:hover:text-[#ff8785]\", \"transition-colors\", 3, \"click\"], [1, \"fas\", \"fa-times\", \"text-xl\"], [3, \"ngSubmit\"], [\"createUserForm\", \"ngForm\"], [1, \"mb-4\"], [\"for\", \"fullName\", 1, \"block\", \"text-sm\", \"font-medium\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"mb-2\"], [1, \"text-[#ff6b69]\"], [\"type\", \"text\", \"id\", \"fullName\", \"name\", \"fullName\", \"required\", \"\", \"placeholder\", \"Enter full name\", \"autocomplete\", \"name\", 1, \"w-full\", \"px-3\", \"py-2.5\", \"rounded-lg\", \"border\", \"border-[#bdc6cc]\", \"dark:border-[#2a2a2a]\", \"bg-white\", \"dark:bg-[#1a1a1a]\", \"text-[#2c3e50]\", \"dark:text-[#e0e0e0]\", \"focus:outline-none\", \"focus:border-[#4f5fad]\", \"dark:focus:border-[#6d78c9]\", \"focus:ring-2\", \"focus:ring-[#4f5fad]/20\", \"dark:focus:ring-[#6d78c9]/20\", \"transition-all\", 3, \"ngModel\", \"ngModelChange\"], [\"for\", \"email\", 1, \"block\", \"text-sm\", \"font-medium\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"mb-2\"], [\"type\", \"email\", \"id\", \"email\", \"name\", \"email\", \"required\", \"\", \"email\", \"\", \"placeholder\", \"Enter email address\", \"autocomplete\", \"email\", 1, \"w-full\", \"px-3\", \"py-2.5\", \"rounded-lg\", \"border\", \"border-[#bdc6cc]\", \"dark:border-[#2a2a2a]\", \"bg-white\", \"dark:bg-[#1a1a1a]\", \"text-[#2c3e50]\", \"dark:text-[#e0e0e0]\", \"focus:outline-none\", \"focus:border-[#4f5fad]\", \"dark:focus:border-[#6d78c9]\", \"focus:ring-2\", \"focus:ring-[#4f5fad]/20\", \"dark:focus:ring-[#6d78c9]/20\", \"transition-all\", 3, \"ngModel\", \"ngModelChange\"], [\"for\", \"role\", 1, \"block\", \"text-sm\", \"font-medium\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"mb-2\"], [\"id\", \"role\", \"name\", \"role\", \"required\", \"\", 1, \"w-full\", \"px-3\", \"py-2.5\", \"rounded-lg\", \"border\", \"border-[#bdc6cc]\", \"dark:border-[#2a2a2a]\", \"bg-white\", \"dark:bg-[#1a1a1a]\", \"text-[#2c3e50]\", \"dark:text-[#e0e0e0]\", \"focus:outline-none\", \"focus:border-[#4f5fad]\", \"dark:focus:border-[#6d78c9]\", \"focus:ring-2\", \"focus:ring-[#4f5fad]/20\", \"dark:focus:ring-[#6d78c9]/20\", \"transition-all\", 3, \"ngModel\", \"ngModelChange\"], [\"value\", \"student\"], [\"value\", \"teacher\"], [\"value\", \"admin\"], [1, \"bg-[#e3f2fd]\", \"dark:bg-[#1a237e]/10\", \"border\", \"border-[#2196f3]/30\", \"dark:border-[#2196f3]/20\", \"rounded-lg\", \"p-4\", \"mb-6\"], [1, \"fas\", \"fa-info-circle\", \"text-[#2196f3]\", \"dark:text-[#64b5f6]\", \"mr-3\", \"mt-0.5\"], [1, \"text-sm\", \"font-medium\", \"text-[#1976d2]\", \"dark:text-[#64b5f6]\", \"mb-1\"], [1, \"text-xs\", \"text-[#1976d2]/80\", \"dark:text-[#64b5f6]/80\"], [1, \"flex\", \"justify-end\", \"space-x-3\"], [\"type\", \"button\", 1, \"px-4\", \"py-2.5\", \"text-sm\", \"font-medium\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"bg-[#f8fafc]\", \"dark:bg-[#2a2a2a]\", \"border\", \"border-[#bdc6cc]\", \"dark:border-[#2a2a2a]\", \"rounded-lg\", \"hover:bg-[#edf1f4]\", \"dark:hover:bg-[#1a1a1a]\", \"transition-colors\", 3, \"click\"], [\"type\", \"submit\", 1, \"px-4\", \"py-2.5\", \"text-sm\", \"font-medium\", \"text-white\", \"rounded-lg\", \"relative\", \"overflow-hidden\", \"group\", \"transition-all\", \"duration-300\", \"disabled:opacity-50\", \"disabled:cursor-not-allowed\", 3, \"disabled\"], [1, \"absolute\", \"inset-0\", \"bg-gradient-to-r\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#4f5fad]\", \"dark:to-[#6d78c9]\", \"rounded-lg\"], [1, \"relative\", \"z-10\", \"flex\", \"items-center\"], [\"class\", \"fas fa-user-plus mr-2\", 4, \"ngIf\"], [\"class\", \"fas fa-spinner fa-spin mr-2\", 4, \"ngIf\"], [1, \"fas\", \"fa-spinner\", \"fa-spin\", \"mr-2\"]],\n      template: function DashboardComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1);\n          i0.ɵɵelement(2, \"div\", 2)(3, \"div\", 3);\n          i0.ɵɵelementStart(4, \"div\", 4)(5, \"div\", 5);\n          i0.ɵɵelement(6, \"div\", 6)(7, \"div\", 6)(8, \"div\", 6)(9, \"div\", 6)(10, \"div\", 6)(11, \"div\", 6)(12, \"div\", 6)(13, \"div\", 6)(14, \"div\", 6)(15, \"div\", 6)(16, \"div\", 6);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(17, \"div\", 7)(18, \"div\", 8)(19, \"div\")(20, \"h1\", 9);\n          i0.ɵɵtext(21, \" Admin Dashboard \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(22, \"p\", 10);\n          i0.ɵɵtext(23, \" Manage users and system settings \");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(24, DashboardComponent_div_24_Template, 10, 1, \"div\", 11);\n          i0.ɵɵtemplate(25, DashboardComponent_div_25_Template, 10, 1, \"div\", 12);\n          i0.ɵɵtemplate(26, DashboardComponent_div_26_Template, 4, 0, \"div\", 13);\n          i0.ɵɵtemplate(27, DashboardComponent_div_27_Template, 89, 16, \"div\", 14);\n          i0.ɵɵtemplate(28, DashboardComponent_div_28_Template, 14, 2, \"div\", 15);\n          i0.ɵɵtemplate(29, DashboardComponent_div_29_Template, 28, 1, \"div\", 16);\n          i0.ɵɵtemplate(30, DashboardComponent_div_30_Template, 14, 2, \"div\", 16);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(31, DashboardComponent_div_31_Template, 54, 9, \"div\", 17);\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(24);\n          i0.ɵɵproperty(\"ngIf\", ctx.message);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.error);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.loading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.loading && ctx.filteredUsers.length > 0);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.loading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.loading && ctx.filteredUsers.length > 0);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.loading && ctx.filteredUsers.length === 0);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.showCreateUserModal);\n        }\n      },\n      dependencies: [i4.NgClass, i4.NgForOf, i4.NgIf, i5.ɵNgNoValidate, i5.NgSelectOption, i5.ɵNgSelectMultipleOption, i5.DefaultValueAccessor, i5.SelectControlValueAccessor, i5.NgControlStatus, i5.NgControlStatusGroup, i5.RequiredValidator, i5.EmailValidator, i5.NgModel, i5.NgForm, i4.TitleCasePipe],\n      styles: [\"[_nghost-%COMP%] {\\n  display: block;\\n}\\n[_nghost-%COMP%]   .min-h-screen[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, var(--dark-bg), var(--medium-bg));\\n  background-image: linear-gradient(rgba(0, 247, 255, 0.03) 1px, transparent 1px), linear-gradient(90deg, rgba(0, 247, 255, 0.03) 1px, transparent 1px);\\n  background-size: 20px 20px;\\n}\\n\\n.grid[_ngcontent-%COMP%]   .bg-white[_ngcontent-%COMP%] {\\n  background: rgba(31, 41, 55, 0.7);\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  border: 1px solid rgba(0, 247, 255, 0.1);\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2), inset 0 0 0 1px rgba(255, 255, 255, 0.05);\\n  transition: all 0.3s ease;\\n}\\n.grid[_ngcontent-%COMP%]   .bg-white[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.3), inset 0 0 0 1px rgba(255, 255, 255, 0.1), 0 0 15px rgba(0, 247, 255, 0.2);\\n  border-color: rgba(0, 247, 255, 0.3);\\n}\\n.grid[_ngcontent-%COMP%]   .bg-white[_ngcontent-%COMP%]   .h-1\\\\.5[_ngcontent-%COMP%] {\\n  height: 4px;\\n  background: rgba(0, 0, 0, 0.3);\\n  overflow: hidden;\\n  border-radius: 4px;\\n}\\n.grid[_ngcontent-%COMP%]   .bg-white[_ngcontent-%COMP%]   .h-1\\\\.5 div[_ngcontent-%COMP%] {\\n  position: relative;\\n  overflow: hidden;\\n}\\n.grid[_ngcontent-%COMP%]   .bg-white[_ngcontent-%COMP%]   .h-1\\\\.5 div[_ngcontent-%COMP%]::after {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);\\n  animation: _ngcontent-%COMP%_shimmer 2s infinite;\\n}\\n.grid[_ngcontent-%COMP%]   .bg-white[_ngcontent-%COMP%]   .h-1\\\\.5[_ngcontent-%COMP%]   .bg-primary-light[_ngcontent-%COMP%], .grid[_ngcontent-%COMP%]   .bg-white[_ngcontent-%COMP%]   .h-1\\\\.5[_ngcontent-%COMP%]   .bg-primary-dark[_ngcontent-%COMP%] {\\n  background: linear-gradient(90deg, var(--accent-color-dark), var(--accent-color));\\n}\\n.grid[_ngcontent-%COMP%]   .bg-white[_ngcontent-%COMP%]   .h-1\\\\.5[_ngcontent-%COMP%]   .bg-green-500[_ngcontent-%COMP%], .grid[_ngcontent-%COMP%]   .bg-white[_ngcontent-%COMP%]   .h-1\\\\.5[_ngcontent-%COMP%]   .bg-green-600[_ngcontent-%COMP%] {\\n  background: linear-gradient(90deg, #059669, #10B981);\\n}\\n.grid[_ngcontent-%COMP%]   .bg-white[_ngcontent-%COMP%]   .h-1\\\\.5[_ngcontent-%COMP%]   .bg-red-500[_ngcontent-%COMP%], .grid[_ngcontent-%COMP%]   .bg-white[_ngcontent-%COMP%]   .h-1\\\\.5[_ngcontent-%COMP%]   .bg-red-600[_ngcontent-%COMP%] {\\n  background: linear-gradient(90deg, #DC2626, #EF4444);\\n}\\n.grid[_ngcontent-%COMP%]   .bg-white[_ngcontent-%COMP%]   .h-1\\\\.5[_ngcontent-%COMP%]   .bg-blue-500[_ngcontent-%COMP%], .grid[_ngcontent-%COMP%]   .bg-white[_ngcontent-%COMP%]   .h-1\\\\.5[_ngcontent-%COMP%]   .bg-blue-600[_ngcontent-%COMP%] {\\n  background: linear-gradient(90deg, #3B82F6, #60A5FA);\\n}\\n.grid[_ngcontent-%COMP%]   .bg-white[_ngcontent-%COMP%]   .h-1\\\\.5[_ngcontent-%COMP%]   .bg-purple-500[_ngcontent-%COMP%], .grid[_ngcontent-%COMP%]   .bg-white[_ngcontent-%COMP%]   .h-1\\\\.5[_ngcontent-%COMP%]   .bg-purple-600[_ngcontent-%COMP%] {\\n  background: linear-gradient(90deg, #8B5CF6, #A78BFA);\\n}\\n.grid[_ngcontent-%COMP%]   .bg-white[_ngcontent-%COMP%]   .h-1\\\\.5[_ngcontent-%COMP%]   .bg-yellow-500[_ngcontent-%COMP%], .grid[_ngcontent-%COMP%]   .bg-white[_ngcontent-%COMP%]   .h-1\\\\.5[_ngcontent-%COMP%]   .bg-yellow-600[_ngcontent-%COMP%] {\\n  background: linear-gradient(90deg, #F59E0B, #FBBF24);\\n}\\n\\ntable[_ngcontent-%COMP%] {\\n  width: 100%;\\n  border-collapse: separate;\\n  border-spacing: 0;\\n}\\ntable[_ngcontent-%COMP%]   thead[_ngcontent-%COMP%]   th[_ngcontent-%COMP%] {\\n  background: rgba(17, 24, 39, 0.7);\\n  -webkit-backdrop-filter: blur(4px);\\n          backdrop-filter: blur(4px);\\n  color: var(--accent-color);\\n  font-weight: 500;\\n  letter-spacing: 1px;\\n  text-transform: uppercase;\\n  padding: 12px 16px;\\n  position: relative;\\n}\\ntable[_ngcontent-%COMP%]   thead[_ngcontent-%COMP%]   th[_ngcontent-%COMP%]:first-child {\\n  border-top-left-radius: var(--border-radius-md);\\n}\\ntable[_ngcontent-%COMP%]   thead[_ngcontent-%COMP%]   th[_ngcontent-%COMP%]:last-child {\\n  border-top-right-radius: var(--border-radius-md);\\n}\\ntable[_ngcontent-%COMP%]   thead[_ngcontent-%COMP%]   th[_ngcontent-%COMP%]::after {\\n  content: \\\"\\\";\\n  position: absolute;\\n  left: 0;\\n  bottom: 0;\\n  width: 100%;\\n  height: 1px;\\n  background: linear-gradient(90deg, transparent, rgba(0, 247, 255, 0.5), transparent);\\n}\\ntable[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%] {\\n  transition: all 0.2s ease;\\n}\\ntable[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]:hover {\\n  background: rgba(0, 247, 255, 0.05) !important;\\n}\\ntable[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]:hover   td[_ngcontent-%COMP%] {\\n  color: white;\\n}\\ntable[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%] {\\n  padding: 12px 16px;\\n  transition: all 0.2s ease;\\n}\\ntable[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   .flex-shrink-0[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, var(--accent-color), var(--secondary-color));\\n  box-shadow: var(--glow-effect);\\n}\\n\\n.badge[_ngcontent-%COMP%] {\\n  display: inline-flex;\\n  align-items: center;\\n  padding: 0.25rem 0.5rem;\\n  border-radius: 9999px;\\n  font-size: 0.75rem;\\n  font-weight: 500;\\n}\\n.badge.verified[_ngcontent-%COMP%] {\\n  background-color: rgba(5, 150, 105, 0.1);\\n  color: #10B981;\\n  border: 1px solid rgba(16, 185, 129, 0.2);\\n}\\n.badge.not-verified[_ngcontent-%COMP%] {\\n  background-color: rgba(239, 68, 68, 0.1);\\n  color: #EF4444;\\n  border: 1px solid rgba(239, 68, 68, 0.2);\\n}\\n.badge.active[_ngcontent-%COMP%] {\\n  background-color: rgba(5, 150, 105, 0.1);\\n  color: #10B981;\\n  border: 1px solid rgba(16, 185, 129, 0.2);\\n}\\n.badge.inactive[_ngcontent-%COMP%] {\\n  background-color: rgba(156, 163, 175, 0.1);\\n  color: #9CA3AF;\\n  border: 1px solid rgba(156, 163, 175, 0.2);\\n}\\n\\nbutton[_ngcontent-%COMP%] {\\n  transition: all 0.2s ease;\\n}\\nbutton[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-1px);\\n  box-shadow: var(--glow-effect);\\n}\\nbutton[_ngcontent-%COMP%]:active {\\n  transform: translateY(0);\\n}\\n\\ninput[type=text][_ngcontent-%COMP%] {\\n  background: rgba(31, 41, 55, 0.7);\\n  border: 1px solid rgba(0, 247, 255, 0.1);\\n  color: white;\\n  transition: all 0.3s ease;\\n}\\ninput[type=text][_ngcontent-%COMP%]:focus {\\n  border-color: var(--accent-color);\\n  box-shadow: 0 0 0 1px rgba(0, 247, 255, 0.2), var(--glow-effect);\\n  outline: none;\\n}\\ninput[type=text][_ngcontent-%COMP%]::placeholder {\\n  color: rgba(156, 163, 175, 0.7);\\n}\\n\\nselect[_ngcontent-%COMP%] {\\n  background: rgba(31, 41, 55, 0.7);\\n  border: 1px solid rgba(0, 247, 255, 0.1);\\n  color: white;\\n  transition: all 0.3s ease;\\n  appearance: none;\\n  background-image: url(\\\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='%2300f7ff'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M19 9l-7 7-7-7'%3E%3C/path%3E%3C/svg%3E\\\");\\n  background-repeat: no-repeat;\\n  background-position: right 0.5rem center;\\n  background-size: 1rem;\\n  padding-right: 2rem;\\n}\\nselect[_ngcontent-%COMP%]:focus {\\n  border-color: var(--accent-color);\\n  box-shadow: 0 0 0 1px rgba(0, 247, 255, 0.2), var(--glow-effect);\\n  outline: none;\\n}\\nselect[_ngcontent-%COMP%]   option[_ngcontent-%COMP%] {\\n  background-color: #1F2937;\\n}\\n\\n@keyframes _ngcontent-%COMP%_shimmer {\\n  0% {\\n    transform: translateX(-100%);\\n  }\\n  100% {\\n    transform: translateX(100%);\\n  }\\n}\\n.animate-spin[_ngcontent-%COMP%] {\\n  border-color: var(--accent-color);\\n  box-shadow: 0 0 15px rgba(0, 247, 255, 0.5);\\n}\\n\\n/*# sourceMappingURL=data:application/json;base64,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 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵtext", "ɵɵadvance", "ɵɵtextInterpolate1", "ctx_r0", "message", "ctx_r1", "error", "ctx_r3", "users", "length", "getActiveCount", "getInactiveCount", "ɵɵstyleProp", "getStudentCount", "getTeacher<PERSON>ount", "getAdminCount", "ɵɵlistener", "DashboardComponent_div_28_button_13_Template_button_click_0_listener", "ɵɵrestoreView", "_r10", "ctx_r9", "ɵɵnextContext", "ɵɵresetView", "clearSearch", "DashboardComponent_div_28_Template_button_click_1_listener", "_r12", "ctx_r11", "openCreateUserModal", "DashboardComponent_div_28_Template_input_input_10_listener", "$event", "ctx_r13", "searchTerm", "target", "value", "searchUsers", "ɵɵtemplate", "DashboardComponent_div_28_button_13_Template", "ɵɵproperty", "ctx_r4", "role_r17", "ɵɵpipeBind1", "DashboardComponent_div_29_tr_27_Template_select_change_23_listener", "restoredCtx", "_r19", "user_r15", "$implicit", "ctx_r18", "onRoleChange", "_id", "DashboardComponent_div_29_tr_27_option_24_Template", "DashboardComponent_div_29_tr_27_Template_button_click_28_listener", "ctx_r20", "toggleUserActivation", "isActive", "DashboardComponent_div_29_tr_27_Template_button_click_34_listener", "ctx_r21", "onDeleteUser", "DashboardComponent_div_29_tr_27_Template_button_click_38_listener", "ctx_r22", "showUserDetails", "ɵɵtextInterpolate", "fullName", "char<PERSON>t", "email", "verified", "ɵɵclassMap", "role", "ctx_r14", "roles", "DashboardComponent_div_29_tr_27_Template", "ctx_r5", "filteredUsers", "DashboardComponent_div_30_div_13_Template_button_click_1_listener", "_r25", "ctx_r24", "DashboardComponent_div_30_div_13_Template", "ctx_r6", "DashboardComponent_div_31_Template_div_click_0_listener", "_r30", "ctx_r29", "closeCreateUserModal", "DashboardComponent_div_31_Template_div_click_1_listener", "stopPropagation", "DashboardComponent_div_31_Template_button_click_8_listener", "ctx_r32", "DashboardComponent_div_31_Template_form_ngSubmit_11_listener", "_r26", "ɵɵreference", "ctx_r33", "onCreateUser", "DashboardComponent_div_31_Template_input_ngModelChange_18_listener", "ctx_r34", "newUser", "DashboardComponent_div_31_Template_input_ngModelChange_24_listener", "ctx_r35", "DashboardComponent_div_31_Template_select_ngModelChange_30_listener", "ctx_r36", "DashboardComponent_div_31_Template_button_click_46_listener", "ctx_r37", "DashboardComponent_div_31_i_51_Template", "DashboardComponent_div_31_i_52_Template", "ctx_r7", "ɵɵclassProp", "valid", "creatingUser", "DashboardComponent", "constructor", "authService", "router", "toastService", "loading", "currentUser", "showCreateUserModal", "ngOnInit", "loadUserData", "token", "localStorage", "getItem", "userStr", "navigate", "JSON", "parse", "getAllUsers", "subscribe", "next", "res", "err", "trim", "term", "toLowerCase", "filter", "user", "includes", "userId", "newRole", "updateUserRole", "userIndex", "findIndex", "u", "filteredIndex", "setTimeout", "confirmDelete", "confirm", "deleteUser", "currentStatus", "newStatus", "action", "find", "userName", "firstName", "confirmAction", "statusText", "successMessage", "showSuccess", "applyFilters", "errorMessage", "showError", "logout", "resetNewUserForm", "form", "invalid", "createUser", "push", "ɵɵdirectiveInject", "i1", "AuthService", "i2", "Router", "i3", "ToastService", "selectors", "decls", "vars", "consts", "template", "DashboardComponent_Template", "rf", "ctx", "DashboardComponent_div_24_Template", "DashboardComponent_div_25_Template", "DashboardComponent_div_26_Template", "DashboardComponent_div_27_Template", "DashboardComponent_div_28_Template", "DashboardComponent_div_29_Template", "DashboardComponent_div_30_Template", "DashboardComponent_div_31_Template"], "sources": ["C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\frontend\\src\\app\\views\\admin\\dashboard\\dashboard.component.ts", "C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\frontend\\src\\app\\views\\admin\\dashboard\\dashboard.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { AuthService } from 'src/app/services/auth.service';\r\nimport { ToastService } from 'src/app/services/toast.service';\r\nimport { Router } from '@angular/router';\r\n\r\n@Component({\r\n  selector: 'app-dashboard',\r\n  templateUrl: './dashboard.component.html',\r\n  styleUrls: ['./dashboard.component.css'],\r\n})\r\nexport class DashboardComponent implements OnInit {\r\n  users: any[] = [];\r\n  error = '';\r\n  message = '';\r\n  roles = ['student', 'teacher', 'admin'];\r\n  loading = true;\r\n  currentUser: any = null;\r\n  searchTerm = '';\r\n  filteredUsers: any[] = [];\r\n\r\n  // Create user modal properties\r\n  showCreateUserModal = false;\r\n  creatingUser = false;\r\n  newUser = {\r\n    fullName: '',\r\n    email: '',\r\n    role: 'student'\r\n  };\r\n\r\n  constructor(\r\n    private authService: AuthService,\r\n    private router: Router,\r\n    private toastService: ToastService\r\n  ) {}\r\n\r\n  ngOnInit(): void {\r\n    this.loadUserData();\r\n  }\r\n\r\n  loadUserData(): void {\r\n    this.loading = true;\r\n    const token = localStorage.getItem('token');\r\n    const userStr = localStorage.getItem('user');\r\n\r\n    if (!token || !userStr) {\r\n      this.router.navigate(['/admin/login']);\r\n      return;\r\n    }\r\n\r\n    this.currentUser = JSON.parse(userStr);\r\n\r\n    // Check if user is admin\r\n    if (this.currentUser.role !== 'admin') {\r\n      this.router.navigate(['/']);\r\n      return;\r\n    }\r\n\r\n    this.authService.getAllUsers(token).subscribe({\r\n      next: (res: any) => {\r\n        this.users = res;\r\n        this.filteredUsers = [...this.users];\r\n        this.loading = false;\r\n      },\r\n      error: (err) => {\r\n        this.error = err.error?.message || 'Failed to fetch users';\r\n        this.loading = false;\r\n      },\r\n    });\r\n  }\r\n  searchUsers(): void {\r\n    if (!this.searchTerm.trim()) {\r\n      this.filteredUsers = [...this.users];\r\n      return;\r\n    }\r\n\r\n    const term = this.searchTerm.toLowerCase().trim();\r\n    this.filteredUsers = this.users.filter(\r\n      (user) =>\r\n        user.fullName.toLowerCase().includes(term) ||\r\n        user.email.toLowerCase().includes(term) ||\r\n        user.role.toLowerCase().includes(term)\r\n    );\r\n  }\r\n  clearSearch(): void {\r\n    this.searchTerm = '';\r\n    this.filteredUsers = [...this.users];\r\n  }\r\n\r\n  onRoleChange(userId: string, newRole: string) {\r\n    const token = localStorage.getItem('token');\r\n    this.authService.updateUserRole(userId, newRole, token!).subscribe({\r\n      next: (res: any) => {\r\n        this.message = res.message;\r\n        this.error = '';\r\n\r\n        // Update the user in the local arrays\r\n        const userIndex = this.users.findIndex((u) => u._id === userId);\r\n        if (userIndex !== -1) {\r\n          this.users[userIndex].role = newRole;\r\n        }\r\n\r\n        const filteredIndex = this.filteredUsers.findIndex(\r\n          (u) => u._id === userId\r\n        );\r\n        if (filteredIndex !== -1) {\r\n          this.filteredUsers[filteredIndex].role = newRole;\r\n        }\r\n\r\n        // Auto-hide message after 3 seconds\r\n        setTimeout(() => {\r\n          this.message = '';\r\n        }, 3000);\r\n      },\r\n      error: (err) => {\r\n        this.error = err.error?.message || 'Failed to update role';\r\n        this.message = '';\r\n\r\n        // Auto-hide error after 3 seconds\r\n        setTimeout(() => {\r\n          this.error = '';\r\n        }, 3000);\r\n      },\r\n    });\r\n  }\r\n  onDeleteUser(userId: string) {\r\n    const confirmDelete = confirm('Are you sure you want to delete this user?');\r\n    if (!confirmDelete) return;\r\n\r\n    const token = localStorage.getItem('token');\r\n    this.authService.deleteUser(userId, token!).subscribe({\r\n      next: (res: any) => {\r\n        this.message = res.message;\r\n        this.error = '';\r\n\r\n        // Remove user from both arrays\r\n        this.users = this.users.filter((u) => u._id !== userId);\r\n        this.filteredUsers = this.filteredUsers.filter((u) => u._id !== userId);\r\n\r\n        // Auto-hide message after 3 seconds\r\n        setTimeout(() => {\r\n          this.message = '';\r\n        }, 3000);\r\n      },\r\n      error: (err) => {\r\n        this.error = err.error?.message || 'Failed to delete user';\r\n        this.message = '';\r\n\r\n        // Auto-hide error after 3 seconds\r\n        setTimeout(() => {\r\n          this.error = '';\r\n        }, 3000);\r\n      },\r\n    });\r\n  }\r\n  toggleUserActivation(userId: string, currentStatus: boolean) {\r\n    const newStatus = !currentStatus;\r\n    const action = newStatus ? 'activate' : 'deactivate';\r\n\r\n    // Find the user to get their name for better messaging\r\n    const user = this.users.find(u => u._id === userId);\r\n    const userName = user?.fullName || user?.firstName || 'User';\r\n\r\n    const confirmAction = confirm(\r\n      `Are you sure you want to ${action} ${userName}?`\r\n    );\r\n    if (!confirmAction) return;\r\n\r\n    const token = localStorage.getItem('token');\r\n    this.authService.toggleUserActivation(userId, newStatus, token!).subscribe({\r\n      next: (res: any) => {\r\n        const statusText = newStatus ? 'activated' : 'deactivated';\r\n        const successMessage = `${userName} has been ${statusText} successfully`;\r\n\r\n        // Show success toast\r\n        this.toastService.showSuccess(successMessage);\r\n\r\n        // Clear any existing messages\r\n        this.message = '';\r\n        this.error = '';\r\n\r\n        // Update user in both arrays\r\n        const userIndex = this.users.findIndex((u) => u._id === userId);\r\n        if (userIndex !== -1) {\r\n          this.users[userIndex].isActive = newStatus;\r\n        }\r\n\r\n        const filteredIndex = this.filteredUsers.findIndex(\r\n          (u) => u._id === userId\r\n        );\r\n        if (filteredIndex !== -1) {\r\n          this.filteredUsers[filteredIndex].isActive = newStatus;\r\n        }\r\n\r\n        // Apply filters to refresh the view\r\n        this.applyFilters();\r\n      },\r\n      error: (err) => {\r\n        const statusText = newStatus ? 'activate' : 'deactivate';\r\n        const errorMessage = err.error?.message || `Failed to ${statusText} ${userName}`;\r\n\r\n        // Show error toast\r\n        this.toastService.showError(errorMessage);\r\n\r\n        // Clear any existing messages\r\n        this.message = '';\r\n        this.error = '';\r\n      },\r\n    });\r\n  }\r\n  getStudentCount(): number {\r\n    return this.users.filter((u) => u.role === 'student').length;\r\n  }\r\n  getTeacherCount(): number {\r\n    return this.users.filter((u) => u.role === 'teacher').length;\r\n  }\r\n  getAdminCount(): number {\r\n    return this.users.filter((u) => u.role === 'admin').length;\r\n  }\r\n  getActiveCount(): number {\r\n    return this.users.filter((u) => u.isActive !== false).length;\r\n  }\r\n  getInactiveCount(): number {\r\n    return this.users.filter((u) => u.isActive === false).length;\r\n  }\r\n\r\n  logout() {\r\n    this.authService.logout();\r\n    this.router.navigate(['/admin/login']);\r\n  }\r\n\r\n  showUserDetails(userId: string) {\r\n    this.router.navigate(['/admin/userdetails', userId]);\r\n  }\r\n\r\n  applyFilters() {\r\n    this.searchUsers();\r\n  }\r\n\r\n  // Create user modal methods\r\n  openCreateUserModal() {\r\n    this.showCreateUserModal = true;\r\n    this.resetNewUserForm();\r\n  }\r\n\r\n  closeCreateUserModal() {\r\n    this.showCreateUserModal = false;\r\n    this.resetNewUserForm();\r\n  }\r\n\r\n  resetNewUserForm() {\r\n    this.newUser = {\r\n      fullName: '',\r\n      email: '',\r\n      role: 'student'\r\n    };\r\n    this.creatingUser = false;\r\n  }\r\n\r\n  onCreateUser(form: any) {\r\n    if (form.invalid || this.creatingUser) {\r\n      return;\r\n    }\r\n\r\n    this.creatingUser = true;\r\n    this.error = '';\r\n    this.message = '';\r\n\r\n    const token = localStorage.getItem('token');\r\n\r\n    this.authService.createUser(this.newUser, token!).subscribe({\r\n      next: (res: any) => {\r\n        // Show success message\r\n        this.message = res.message;\r\n        this.error = '';\r\n\r\n        // Add new user to the lists\r\n        this.users.push(res.user);\r\n        this.filteredUsers = [...this.users];\r\n        this.applyFilters();\r\n\r\n        // Show success toast with credentials info\r\n        this.toastService.showSuccess(\r\n          `User created successfully! Login credentials sent to ${res.user.email}`\r\n        );\r\n\r\n        // Close modal and reset form\r\n        this.closeCreateUserModal();\r\n\r\n        // Auto-hide message after 5 seconds\r\n        setTimeout(() => {\r\n          this.message = '';\r\n        }, 5000);\r\n      },\r\n      error: (err) => {\r\n        this.error = err.error?.message || 'Failed to create user';\r\n        this.message = '';\r\n        this.creatingUser = false;\r\n\r\n        // Show error toast\r\n        this.toastService.showError(this.error);\r\n\r\n        // Auto-hide error after 3 seconds\r\n        setTimeout(() => {\r\n          this.error = '';\r\n        }, 3000);\r\n      },\r\n    });\r\n  }\r\n}\r\n", "<!-- Begin Page Content -->\r\n<div\r\n  class=\"container-fluid p-4 md:p-6 bg-[#edf1f4] dark:bg-[#121212] min-h-screen relative\"\r\n>\r\n  <!-- Background decorative elements -->\r\n  <div class=\"absolute inset-0 overflow-hidden pointer-events-none\">\r\n    <div\r\n      class=\"absolute top-[15%] left-[10%] w-64 h-64 rounded-full bg-gradient-to-br from-[#4f5fad]/5 to-transparent dark:from-[#6d78c9]/3 dark:to-transparent blur-3xl\"\r\n    ></div>\r\n    <div\r\n      class=\"absolute bottom-[20%] right-[10%] w-80 h-80 rounded-full bg-gradient-to-tl from-[#4f5fad]/5 to-transparent dark:from-[#6d78c9]/3 dark:to-transparent blur-3xl\"\r\n    ></div>\r\n\r\n    <!-- Grid pattern -->\r\n    <div class=\"absolute inset-0 opacity-5 dark:opacity-[0.03]\">\r\n      <div class=\"h-full grid grid-cols-12\">\r\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\r\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\r\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\r\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\r\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\r\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\r\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\r\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\r\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\r\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\r\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n\r\n  <!-- Page Content -->\r\n  <div class=\"relative z-10\">\r\n    <!-- Page Heading -->\r\n    <div\r\n      class=\"flex flex-col md:flex-row md:items-center md:justify-between mb-8\"\r\n    >\r\n      <div>\r\n        <h1\r\n          class=\"text-2xl font-bold bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] bg-clip-text text-transparent mb-2\"\r\n        >\r\n          Admin Dashboard\r\n        </h1>\r\n        <p class=\"text-sm text-[#6d6870] dark:text-[#a0a0a0]\">\r\n          Manage users and system settings\r\n        </p>\r\n      </div>\r\n\r\n\r\n    </div>\r\n\r\n    <!-- Alerts -->\r\n    <div\r\n      *ngIf=\"message\"\r\n      class=\"bg-[#afcf75]/10 dark:bg-[#afcf75]/5 border border-[#afcf75] dark:border-[#afcf75]/30 rounded-lg p-4 mx-auto max-w-3xl mb-6 backdrop-blur-sm\"\r\n    >\r\n      <div class=\"flex items-start\">\r\n        <div class=\"text-[#2a5a03] dark:text-[#afcf75] mr-3 text-xl relative\">\r\n          <i class=\"fas fa-check-circle\"></i>\r\n          <!-- Glow effect -->\r\n          <div\r\n            class=\"absolute inset-0 bg-[#afcf75]/20 dark:bg-[#afcf75]/20 blur-xl rounded-full transform scale-150 -z-10\"\r\n          ></div>\r\n        </div>\r\n        <div class=\"flex-1\">\r\n          <h3 class=\"font-medium text-[#2a5a03] dark:text-[#afcf75] mb-1\">\r\n            Success\r\n          </h3>\r\n          <p class=\"text-sm text-[#2a5a03]/80 dark:text-[#afcf75]/80\">\r\n            {{ message }}\r\n          </p>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <div\r\n      *ngIf=\"error\"\r\n      class=\"bg-[#ff6b69]/10 dark:bg-[#ff6b69]/5 border border-[#ff6b69] dark:border-[#ff6b69]/30 rounded-lg p-4 mx-auto max-w-3xl mb-6 backdrop-blur-sm\"\r\n    >\r\n      <div class=\"flex items-start\">\r\n        <div class=\"text-[#ff6b69] dark:text-[#ff8785] mr-3 text-xl relative\">\r\n          <i class=\"fas fa-exclamation-triangle\"></i>\r\n          <!-- Glow effect -->\r\n          <div\r\n            class=\"absolute inset-0 bg-[#ff6b69]/20 dark:bg-[#ff8785]/20 blur-xl rounded-full transform scale-150 -z-10\"\r\n          ></div>\r\n        </div>\r\n        <div class=\"flex-1\">\r\n          <h3 class=\"font-medium text-[#ff6b69] dark:text-[#ff8785] mb-1\">\r\n            Error\r\n          </h3>\r\n          <p class=\"text-sm text-[#ff6b69]/80 dark:text-[#ff8785]/80\">\r\n            {{ error }}\r\n          </p>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Loading State -->\r\n    <div *ngIf=\"loading\" class=\"flex justify-center items-center py-20\">\r\n      <div class=\"relative\">\r\n        <div\r\n          class=\"w-14 h-14 border-4 border-[#4f5fad]/20 dark:border-[#6d78c9]/20 border-t-[#4f5fad] dark:border-t-[#6d78c9] rounded-full animate-spin\"\r\n        ></div>\r\n        <!-- Glow effect -->\r\n        <div\r\n          class=\"absolute inset-0 bg-[#4f5fad]/20 dark:bg-[#6d78c9]/20 blur-xl rounded-full transform scale-150 -z-10\"\r\n        ></div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Stats Cards -->\r\n    <div\r\n      *ngIf=\"!loading && filteredUsers.length > 0\"\r\n      class=\"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8\"\r\n    >\r\n      <!-- Total Users Card -->\r\n      <div\r\n        class=\"bg-white dark:bg-[#1e1e1e] rounded-xl shadow-md dark:shadow-[0_4px_20px_rgba(0,0,0,0.2)] overflow-hidden backdrop-blur-sm border border-[#edf1f4]/50 dark:border-[#2a2a2a] relative group hover:shadow-lg dark:hover:shadow-[0_8px_30px_rgba(0,0,0,0.3)] transition-all duration-300 hover:-translate-y-1\"\r\n      >\r\n        <!-- Decorative gradient top border -->\r\n        <div\r\n          class=\"absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad]\"\r\n        ></div>\r\n\r\n        <!-- Glow effect on hover -->\r\n        <div\r\n          class=\"absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] opacity-0 group-hover:opacity-100 blur-md transition-opacity duration-300\"\r\n        ></div>\r\n\r\n        <div class=\"p-6\">\r\n          <div class=\"flex items-center\">\r\n            <div class=\"flex-1\">\r\n              <div\r\n                class=\"text-sm font-medium text-[#6d6870] dark:text-[#a0a0a0] uppercase tracking-wider\"\r\n              >\r\n                Total Users\r\n              </div>\r\n              <div\r\n                class=\"mt-1 text-2xl font-semibold bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] bg-clip-text text-transparent\"\r\n              >\r\n                {{ users.length }}\r\n              </div>\r\n            </div>\r\n            <div class=\"ml-4 relative\">\r\n              <div\r\n                class=\"absolute inset-0 bg-[#4f5fad]/10 dark:bg-[#6d78c9]/10 rounded-full blur-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300 scale-150\"\r\n              ></div>\r\n              <div\r\n                class=\"relative z-10 bg-[#edf1f4]/80 dark:bg-[#2a2a2a]/80 rounded-full p-2.5 backdrop-blur-sm group-hover:scale-110 transition-transform duration-300\"\r\n              >\r\n                <i\r\n                  class=\"fas fa-users text-[#4f5fad] dark:text-[#6d78c9] text-xl\"\r\n                ></i>\r\n              </div>\r\n            </div>\r\n          </div>\r\n          <div\r\n            class=\"mt-4 w-full bg-[#edf1f4] dark:bg-[#2a2a2a] rounded-full h-2.5 overflow-hidden relative\"\r\n          >\r\n            <div\r\n              class=\"absolute inset-0 bg-gradient-to-r from-[#3d4a85]/10 to-[#4f5fad]/10 dark:from-[#6d78c9]/10 dark:to-[#4f5fad]/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300 blur-md\"\r\n            ></div>\r\n            <div\r\n              class=\"bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] h-2.5 rounded-full relative z-10\"\r\n              style=\"width: 100%\"\r\n            >\r\n              <div\r\n                class=\"absolute inset-0 bg-[#00f7ff]/20 rounded-full animate-pulse\"\r\n              ></div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- User Status Card -->\r\n      <div\r\n        class=\"bg-white dark:bg-[#1e1e1e] rounded-xl shadow-md dark:shadow-[0_4px_20px_rgba(0,0,0,0.2)] overflow-hidden backdrop-blur-sm border border-[#edf1f4]/50 dark:border-[#2a2a2a] relative group hover:shadow-lg dark:hover:shadow-[0_8px_30px_rgba(0,0,0,0.3)] transition-all duration-300 hover:-translate-y-1\"\r\n      >\r\n        <!-- Decorative gradient top border -->\r\n        <div\r\n          class=\"absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-[#2a5a03] to-[#afcf75] dark:from-[#2a5a03] dark:to-[#afcf75]\"\r\n        ></div>\r\n\r\n        <!-- Glow effect on hover -->\r\n        <div\r\n          class=\"absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-[#2a5a03] to-[#afcf75] dark:from-[#2a5a03] dark:to-[#afcf75] opacity-0 group-hover:opacity-100 blur-md transition-opacity duration-300\"\r\n        ></div>\r\n\r\n        <div class=\"p-6\">\r\n          <div class=\"flex items-center\">\r\n            <div class=\"flex-1\">\r\n              <div\r\n                class=\"text-sm font-medium text-[#6d6870] dark:text-[#a0a0a0] uppercase tracking-wider\"\r\n              >\r\n                User Status\r\n              </div>\r\n              <div class=\"mt-1 flex space-x-4 text-sm\">\r\n                <div class=\"flex items-center\">\r\n                  <div\r\n                    class=\"w-2 h-2 rounded-full bg-[#afcf75] dark:bg-[#afcf75] mr-1.5 relative\"\r\n                  >\r\n                    <div\r\n                      class=\"absolute inset-0 bg-[#afcf75] rounded-full animate-ping opacity-75\"\r\n                    ></div>\r\n                  </div>\r\n                  <span class=\"text-[#2a5a03] dark:text-[#afcf75] font-medium\"\r\n                    >{{ getActiveCount() }} Active</span\r\n                  >\r\n                </div>\r\n                <div class=\"flex items-center\">\r\n                  <div\r\n                    class=\"w-2 h-2 rounded-full bg-[#ff6b69] dark:bg-[#ff8785] mr-1.5\"\r\n                  ></div>\r\n                  <span class=\"text-[#ff6b69] dark:text-[#ff8785] font-medium\"\r\n                    >{{ getInactiveCount() }} Inactive</span\r\n                  >\r\n                </div>\r\n              </div>\r\n            </div>\r\n            <div class=\"ml-4 relative\">\r\n              <div\r\n                class=\"absolute inset-0 bg-[#afcf75]/10 dark:bg-[#afcf75]/10 rounded-full blur-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300 scale-150\"\r\n              ></div>\r\n              <div\r\n                class=\"relative z-10 bg-[#edf1f4]/80 dark:bg-[#2a2a2a]/80 rounded-full p-2.5 backdrop-blur-sm group-hover:scale-110 transition-transform duration-300\"\r\n              >\r\n                <i\r\n                  class=\"fas fa-check-circle text-[#afcf75] dark:text-[#afcf75] text-xl\"\r\n                ></i>\r\n              </div>\r\n            </div>\r\n          </div>\r\n          <div\r\n            class=\"mt-4 w-full bg-[#edf1f4] dark:bg-[#2a2a2a] rounded-full h-2.5 overflow-hidden relative\"\r\n          >\r\n            <div\r\n              class=\"absolute inset-0 bg-gradient-to-r from-[#2a5a03]/10 to-[#afcf75]/10 dark:from-[#2a5a03]/10 dark:to-[#afcf75]/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300 blur-md\"\r\n            ></div>\r\n            <div class=\"flex h-full relative z-10\">\r\n              <div\r\n                class=\"bg-gradient-to-r from-[#2a5a03] to-[#afcf75] dark:from-[#2a5a03] dark:to-[#afcf75] h-2.5 relative\"\r\n                [style.width.%]=\"\r\n                  users.length ? (getActiveCount() / users.length) * 100 : 0\r\n                \"\r\n              >\r\n                <div\r\n                  class=\"absolute inset-0 bg-[#afcf75]/20 rounded-full animate-pulse\"\r\n                ></div>\r\n              </div>\r\n              <div\r\n                class=\"bg-gradient-to-r from-[#ff6b69] to-[#ff8785] dark:from-[#ff6b69] dark:to-[#ff8785] h-2.5 relative\"\r\n                [style.width.%]=\"\r\n                  users.length ? (getInactiveCount() / users.length) * 100 : 0\r\n                \"\r\n              >\r\n                <div\r\n                  class=\"absolute inset-0 bg-[#ff6b69]/20 rounded-full animate-pulse\"\r\n                ></div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- User Roles Card -->\r\n      <div\r\n        class=\"bg-white dark:bg-[#1e1e1e] rounded-xl shadow-md dark:shadow-[0_4px_20px_rgba(0,0,0,0.2)] overflow-hidden backdrop-blur-sm border border-[#edf1f4]/50 dark:border-[#2a2a2a] relative group hover:shadow-lg dark:hover:shadow-[0_8px_30px_rgba(0,0,0,0.3)] transition-all duration-300 hover:-translate-y-1\"\r\n      >\r\n        <!-- Decorative gradient top border -->\r\n        <div\r\n          class=\"absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-[#4a89ce] to-[#7826b5] dark:from-[#4a89ce] dark:to-[#7826b5]\"\r\n        ></div>\r\n\r\n        <!-- Glow effect on hover -->\r\n        <div\r\n          class=\"absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-[#4a89ce] to-[#7826b5] dark:from-[#4a89ce] dark:to-[#7826b5] opacity-0 group-hover:opacity-100 blur-md transition-opacity duration-300\"\r\n        ></div>\r\n\r\n        <div class=\"p-6\">\r\n          <div class=\"flex items-center\">\r\n            <div class=\"flex-1\">\r\n              <div\r\n                class=\"text-sm font-medium text-[#6d6870] dark:text-[#a0a0a0] uppercase tracking-wider\"\r\n              >\r\n                User Roles\r\n              </div>\r\n              <div class=\"mt-1 flex flex-wrap gap-4 text-sm\">\r\n                <div class=\"flex items-center\">\r\n                  <div\r\n                    class=\"w-2 h-2 rounded-full bg-[#4a89ce] dark:bg-[#4a89ce] mr-1.5\"\r\n                  ></div>\r\n                  <span class=\"text-[#4a89ce] dark:text-[#4a89ce] font-medium\"\r\n                    >{{ getStudentCount() }} Students</span\r\n                  >\r\n                </div>\r\n                <div class=\"flex items-center\">\r\n                  <div\r\n                    class=\"w-2 h-2 rounded-full bg-[#7826b5] dark:bg-[#7826b5] mr-1.5\"\r\n                  ></div>\r\n                  <span class=\"text-[#7826b5] dark:text-[#7826b5] font-medium\"\r\n                    >{{ getTeacherCount() }} Teachers</span\r\n                  >\r\n                </div>\r\n                <div class=\"flex items-center\">\r\n                  <div\r\n                    class=\"w-2 h-2 rounded-full bg-[#4f5fad] dark:bg-[#6d78c9] mr-1.5\"\r\n                  ></div>\r\n                  <span class=\"text-[#4f5fad] dark:text-[#6d78c9] font-medium\"\r\n                    >{{ getAdminCount() }} Admins</span\r\n                  >\r\n                </div>\r\n              </div>\r\n            </div>\r\n            <div class=\"ml-4 relative\">\r\n              <div\r\n                class=\"absolute inset-0 bg-[#4a89ce]/10 dark:bg-[#4a89ce]/10 rounded-full blur-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300 scale-150\"\r\n              ></div>\r\n              <div\r\n                class=\"relative z-10 bg-[#edf1f4]/80 dark:bg-[#2a2a2a]/80 rounded-full p-2.5 backdrop-blur-sm group-hover:scale-110 transition-transform duration-300\"\r\n              >\r\n                <i\r\n                  class=\"fas fa-user-tag text-[#4a89ce] dark:text-[#4a89ce] text-xl\"\r\n                ></i>\r\n              </div>\r\n            </div>\r\n          </div>\r\n          <div\r\n            class=\"mt-4 w-full bg-[#edf1f4] dark:bg-[#2a2a2a] rounded-full h-2.5 overflow-hidden relative\"\r\n          >\r\n            <div\r\n              class=\"absolute inset-0 bg-gradient-to-r from-[#4a89ce]/10 to-[#7826b5]/10 dark:from-[#4a89ce]/10 dark:to-[#7826b5]/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300 blur-md\"\r\n            ></div>\r\n            <div class=\"flex h-full relative z-10\">\r\n              <div\r\n                class=\"bg-gradient-to-r from-[#4a89ce] to-[#4a89ce] dark:from-[#4a89ce] dark:to-[#4a89ce] h-2.5 relative\"\r\n                [style.width.%]=\"\r\n                  users.length ? (getStudentCount() / users.length) * 100 : 0\r\n                \"\r\n              >\r\n                <div\r\n                  class=\"absolute inset-0 bg-[#4a89ce]/20 rounded-full animate-pulse\"\r\n                ></div>\r\n              </div>\r\n              <div\r\n                class=\"bg-gradient-to-r from-[#7826b5] to-[#7826b5] dark:from-[#7826b5] dark:to-[#7826b5] h-2.5 relative\"\r\n                [style.width.%]=\"\r\n                  users.length ? (getTeacherCount() / users.length) * 100 : 0\r\n                \"\r\n              >\r\n                <div\r\n                  class=\"absolute inset-0 bg-[#7826b5]/20 rounded-full animate-pulse\"\r\n                ></div>\r\n              </div>\r\n              <div\r\n                class=\"bg-gradient-to-r from-[#4f5fad] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#6d78c9] h-2.5 relative\"\r\n                [style.width.%]=\"\r\n                  users.length ? (getAdminCount() / users.length) * 100 : 0\r\n                \"\r\n              >\r\n                <div\r\n                  class=\"absolute inset-0 bg-[#4f5fad]/20 dark:bg-[#6d78c9]/20 rounded-full animate-pulse\"\r\n                ></div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n          <div\r\n            class=\"flex justify-between mt-2 text-xs text-[#6d6870] dark:text-[#a0a0a0]\"\r\n          >\r\n            <span>Students</span>\r\n            <span>Teachers</span>\r\n            <span>Admins</span>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- User Management Controls -->\r\n    <div\r\n      *ngIf=\"!loading\"\r\n      class=\"flex flex-col md:flex-row md:items-center md:justify-between mb-6 gap-4\"\r\n    >\r\n      <!-- Create User Button -->\r\n      <button\r\n        (click)=\"openCreateUserModal()\"\r\n        class=\"inline-flex items-center px-4 py-2.5 text-sm font-medium rounded-lg relative overflow-hidden group transition-all duration-300 hover:shadow-lg hover:-translate-y-0.5 w-fit\"\r\n      >\r\n        <!-- Background gradient -->\r\n        <div\r\n          class=\"absolute inset-0 bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#4f5fad] dark:to-[#6d78c9] rounded-lg opacity-90 group-hover:opacity-100 transition-opacity\"\r\n        ></div>\r\n\r\n        <!-- Glow effect -->\r\n        <div\r\n          class=\"absolute inset-0 bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#4f5fad] dark:to-[#6d78c9] rounded-lg opacity-0 group-hover:opacity-30 blur-xl transition-opacity\"\r\n        ></div>\r\n\r\n        <!-- Content -->\r\n        <div class=\"relative z-10 flex items-center text-white\">\r\n          <i class=\"fas fa-user-plus mr-2 group-hover:scale-110 transition-transform\"></i>\r\n          Create User\r\n        </div>\r\n      </button>\r\n\r\n      <!-- Search Box -->\r\n      <div class=\"relative w-full md:w-80 group\">\r\n        <div\r\n          class=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\"\r\n        >\r\n          <i\r\n            class=\"fas fa-search text-[#bdc6cc] dark:text-[#6d6870] group-focus-within:text-[#4f5fad] dark:group-focus-within:text-[#6d78c9] transition-colors\"\r\n          ></i>\r\n        </div>\r\n        <input\r\n          type=\"text\"\r\n          [value]=\"searchTerm\"\r\n          (input)=\"searchTerm = $any($event.target).value; searchUsers()\"\r\n          placeholder=\"Search users by name, email, or role...\"\r\n          class=\"w-full pl-10 pr-10 py-2.5 text-sm rounded-lg border border-[#bdc6cc] dark:border-[#2a2a2a] bg-white dark:bg-[#1e1e1e] text-[#6d6870] dark:text-[#e0e0e0] focus:outline-none focus:border-[#4f5fad] dark:focus:border-[#6d78c9] focus:ring-2 focus:ring-[#4f5fad]/20 dark:focus:ring-[#6d78c9]/20 transition-all\"\r\n        />\r\n        <div\r\n          class=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none opacity-0 group-focus-within:opacity-100 transition-opacity\"\r\n        >\r\n          <div\r\n            class=\"w-0.5 h-4 bg-gradient-to-b from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] rounded-full\"\r\n          ></div>\r\n        </div>\r\n        <button\r\n          *ngIf=\"searchTerm\"\r\n          (click)=\"clearSearch()\"\r\n          class=\"absolute inset-y-0 right-0 pr-3 flex items-center text-[#bdc6cc] dark:text-[#6d6870] hover:text-[#ff6b69] dark:hover:text-[#ff8785] transition-colors\"\r\n        >\r\n          <i class=\"fas fa-times-circle\"></i>\r\n        </button>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Users Table -->\r\n    <div\r\n      *ngIf=\"!loading && filteredUsers.length > 0\"\r\n      class=\"bg-white dark:bg-[#1e1e1e] rounded-xl shadow-md dark:shadow-[0_4px_20px_rgba(0,0,0,0.2)] overflow-hidden mb-8 backdrop-blur-sm border border-[#edf1f4]/50 dark:border-[#2a2a2a] relative\"\r\n    >\r\n      <!-- Decorative gradient top border -->\r\n      <div\r\n        class=\"absolute top-0 left-0 right-0 h-0.5 bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad]\"\r\n      ></div>\r\n\r\n      <div\r\n        class=\"p-5 border-b border-[#edf1f4]/50 dark:border-[#2a2a2a] flex items-center justify-between\"\r\n      >\r\n        <h6\r\n          class=\"font-bold bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] bg-clip-text text-transparent flex items-center\"\r\n        >\r\n          <i class=\"fas fa-users-cog mr-2\"></i>\r\n          User Management\r\n        </h6>\r\n      </div>\r\n      <div class=\"overflow-x-auto\">\r\n        <table\r\n          class=\"w-full divide-y divide-[#edf1f4] dark:divide-[#2a2a2a] table-fixed\"\r\n        >\r\n          <thead class=\"bg-[#f8fafc] dark:bg-[#1a1a1a]\">\r\n            <tr>\r\n              <th\r\n                scope=\"col\"\r\n                class=\"px-3 py-3 text-left text-xs font-medium text-[#4f5fad] dark:text-[#6d78c9] uppercase tracking-wider w-[15%]\"\r\n              >\r\n                Name\r\n              </th>\r\n              <th\r\n                scope=\"col\"\r\n                class=\"px-3 py-3 text-left text-xs font-medium text-[#4f5fad] dark:text-[#6d78c9] uppercase tracking-wider w-[20%]\"\r\n              >\r\n                Email\r\n              </th>\r\n              <th\r\n                scope=\"col\"\r\n                class=\"px-2 py-3 text-center text-xs font-medium text-[#4f5fad] dark:text-[#6d78c9] uppercase tracking-wider w-[8%]\"\r\n              >\r\n                Verified\r\n              </th>\r\n              <th\r\n                scope=\"col\"\r\n                class=\"px-2 py-3 text-center text-xs font-medium text-[#4f5fad] dark:text-[#6d78c9] uppercase tracking-wider w-[8%]\"\r\n              >\r\n                Status\r\n              </th>\r\n              <th\r\n                scope=\"col\"\r\n                class=\"px-2 py-3 text-center text-xs font-medium text-[#4f5fad] dark:text-[#6d78c9] uppercase tracking-wider w-[10%]\"\r\n              >\r\n                Role\r\n              </th>\r\n              <th\r\n                scope=\"col\"\r\n                class=\"px-2 py-3 text-center text-xs font-medium text-[#4f5fad] dark:text-[#6d78c9] uppercase tracking-wider w-[13%]\"\r\n              >\r\n                Activate\r\n              </th>\r\n              <th\r\n                scope=\"col\"\r\n                class=\"px-2 py-3 text-center text-xs font-medium text-[#4f5fad] dark:text-[#6d78c9] uppercase tracking-wider w-[13%]\"\r\n              >\r\n                Delete\r\n              </th>\r\n              <th\r\n                scope=\"col\"\r\n                class=\"px-2 py-3 text-center text-xs font-medium text-[#4f5fad] dark:text-[#6d78c9] uppercase tracking-wider w-[13%]\"\r\n              >\r\n                Details\r\n              </th>\r\n            </tr>\r\n          </thead>\r\n          <tbody\r\n            class=\"bg-white dark:bg-[#1e1e1e] divide-y divide-[#edf1f4] dark:divide-[#2a2a2a]\"\r\n          >\r\n            <tr\r\n              *ngFor=\"let user of filteredUsers\"\r\n              class=\"hover:bg-[#f8fafc] dark:hover:bg-[#1a1a1a] transition-colors\"\r\n            >\r\n              <td class=\"px-3 py-3 whitespace-nowrap truncate\">\r\n                <div class=\"flex items-center\">\r\n                  <div\r\n                    class=\"flex-shrink-0 h-8 w-8 rounded-full bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] text-white flex items-center justify-center text-xs shadow-md relative group\"\r\n                  >\r\n                    <div\r\n                      class=\"absolute inset-0 bg-[#4f5fad]/20 dark:bg-[#6d78c9]/20 rounded-full blur-md opacity-0 group-hover:opacity-100 transition-opacity\"\r\n                    ></div>\r\n                    <span class=\"relative z-10\">{{\r\n                      user.fullName.charAt(0)\r\n                    }}</span>\r\n                  </div>\r\n                  <div class=\"ml-3\">\r\n                    <div\r\n                      class=\"text-sm font-medium text-[#4f5fad] dark:text-[#6d78c9] truncate max-w-[120px]\"\r\n                    >\r\n                      {{ user.fullName }}\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </td>\r\n              <td class=\"px-3 py-3 whitespace-nowrap\">\r\n                <div\r\n                  class=\"text-sm text-[#6d6870] dark:text-[#a0a0a0] truncate max-w-[150px]\"\r\n                >\r\n                  {{ user.email }}\r\n                </div>\r\n              </td>\r\n              <td class=\"px-2 py-3 whitespace-nowrap text-center\">\r\n                <span\r\n                  class=\"px-2 py-1 text-xs rounded-lg inline-flex items-center justify-center\"\r\n                  [ngClass]=\"\r\n                    user.verified\r\n                      ? 'bg-[#afcf75]/10 dark:bg-[#afcf75]/5 text-[#2a5a03] dark:text-[#afcf75]'\r\n                      : 'bg-[#ff6b69]/10 dark:bg-[#ff6b69]/5 text-[#ff6b69] dark:text-[#ff8785]'\r\n                  \"\r\n                >\r\n                  <i\r\n                    [class]=\"\r\n                      user.verified\r\n                        ? 'fas fa-check-circle mr-1'\r\n                        : 'fas fa-times-circle mr-1'\r\n                    \"\r\n                  ></i>\r\n                  {{ user.verified ? \"Yes\" : \"No\" }}\r\n                </span>\r\n              </td>\r\n              <td class=\"px-2 py-3 whitespace-nowrap text-center\">\r\n                <span\r\n                  class=\"px-3 py-1.5 text-xs rounded-full inline-flex items-center justify-center font-semibold transition-all duration-300\"\r\n                  [ngClass]=\"\r\n                    user.isActive !== false\r\n                      ? 'bg-[#afcf75]/15 dark:bg-[#afcf75]/10 text-[#2a5a03] dark:text-[#afcf75] border border-[#afcf75]/30 shadow-sm'\r\n                      : 'bg-[#ff6b69]/15 dark:bg-[#ff6b69]/10 text-[#ff6b69] dark:text-[#ff8785] border border-[#ff6b69]/30 shadow-sm'\r\n                  \"\r\n                >\r\n                  <i\r\n                    [class]=\"\r\n                      user.isActive !== false\r\n                        ? 'fas fa-check-circle mr-1.5 text-[10px]'\r\n                        : 'fas fa-times-circle mr-1.5 text-[10px]'\r\n                    \"\r\n                  ></i>\r\n                  {{ user.isActive !== false ? \"Active\" : \"Deactivated\" }}\r\n                </span>\r\n              </td>\r\n              <td class=\"px-2 py-3 whitespace-nowrap text-center\">\r\n                <div class=\"relative group\">\r\n                  <select\r\n                    class=\"w-full px-2 py-1.5 text-xs rounded-lg border border-[#bdc6cc] dark:border-[#2a2a2a] bg-white dark:bg-[#1a1a1a] text-[#6d6870] dark:text-[#e0e0e0] focus:outline-none focus:border-[#4f5fad] dark:focus:border-[#6d78c9] focus:ring-1 focus:ring-[#4f5fad]/20 dark:focus:ring-[#6d78c9]/20 transition-all appearance-none pr-7\"\r\n                    [value]=\"user.role\"\r\n                    (change)=\"onRoleChange(user._id, $any($event.target).value)\"\r\n                  >\r\n                    <option *ngFor=\"let role of roles\" [value]=\"role\">\r\n                      {{ role | titlecase }}\r\n                    </option>\r\n                  </select>\r\n                  <div\r\n                    class=\"absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none text-[#6d6870] dark:text-[#a0a0a0]\"\r\n                  >\r\n                    <i class=\"fas fa-chevron-down text-xs\"></i>\r\n                  </div>\r\n                </div>\r\n              </td>\r\n              <td class=\"px-2 py-3 whitespace-nowrap text-center\">\r\n                <button\r\n                  class=\"px-3 py-2 text-xs rounded-lg font-semibold flex items-center justify-center mx-auto transition-all duration-300 w-full relative overflow-hidden group border shadow-sm hover:shadow-md transform hover:scale-105\"\r\n                  [ngClass]=\"\r\n                    user.isActive !== false\r\n                      ? 'bg-[#ff6b69]/10 dark:bg-[#ff6b69]/5 text-[#ff6b69] dark:text-[#ff8785] hover:bg-[#ff6b69]/20 dark:hover:bg-[#ff6b69]/10 border-[#ff6b69]/30 hover:border-[#ff6b69]/50'\r\n                      : 'bg-[#afcf75]/10 dark:bg-[#afcf75]/5 text-[#2a5a03] dark:text-[#afcf75] hover:bg-[#afcf75]/20 dark:hover:bg-[#afcf75]/10 border-[#afcf75]/30 hover:border-[#afcf75]/50'\r\n                  \"\r\n                  (click)=\"\r\n                    toggleUserActivation(user._id, user.isActive !== false)\r\n                  \"\r\n                  [title]=\"\r\n                    user.isActive !== false\r\n                      ? 'Click to deactivate this user account'\r\n                      : 'Click to activate this user account'\r\n                  \"\r\n                >\r\n                  <!-- Background animation -->\r\n                  <div class=\"absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-300\"\r\n                       [ngClass]=\"\r\n                         user.isActive !== false\r\n                           ? 'bg-gradient-to-r from-[#ff6b69]/5 to-[#ff6b69]/10'\r\n                           : 'bg-gradient-to-r from-[#afcf75]/5 to-[#afcf75]/10'\r\n                       \">\r\n                  </div>\r\n\r\n                  <i\r\n                    class=\"relative z-10 mr-2 transition-transform duration-300 group-hover:scale-110\"\r\n                    [class]=\"\r\n                      user.isActive !== false\r\n                        ? 'fas fa-user-slash'\r\n                        : 'fas fa-user-check'\r\n                    \"\r\n                  ></i>\r\n                  <span class=\"relative z-10\">\r\n                    {{ user.isActive !== false ? \"Deactivate\" : \"Activate\" }}\r\n                  </span>\r\n                </button>\r\n              </td>\r\n              <td class=\"px-2 py-3 whitespace-nowrap text-center\">\r\n                <button\r\n                  class=\"px-2 py-1.5 text-xs rounded-lg bg-[#ff6b69]/10 dark:bg-[#ff6b69]/5 text-[#ff6b69] dark:text-[#ff8785] hover:bg-[#ff6b69]/20 dark:hover:bg-[#ff6b69]/10 font-medium flex items-center justify-center mx-auto transition-all w-full\"\r\n                  (click)=\"onDeleteUser(user._id)\"\r\n                  title=\"Supprimer définitivement cet utilisateur\"\r\n                >\r\n                  <i class=\"fas fa-trash-alt mr-1.5\"></i>\r\n                  Delete\r\n                </button>\r\n              </td>\r\n              <td class=\"px-2 py-3 whitespace-nowrap text-center\">\r\n                <button\r\n                  class=\"px-2 py-1.5 text-xs rounded-lg bg-[#4f5fad]/10 dark:bg-[#6d78c9]/5 text-[#4f5fad] dark:text-[#6d78c9] hover:bg-[#4f5fad]/20 dark:hover:bg-[#6d78c9]/10 font-medium flex items-center justify-center mx-auto transition-all w-full\"\r\n                  (click)=\"showUserDetails(user._id)\"\r\n                >\r\n                  <i class=\"fas fa-eye mr-1.5\"></i>\r\n                  Details\r\n                </button>\r\n              </td>\r\n            </tr>\r\n          </tbody>\r\n        </table>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- No Users State -->\r\n    <div\r\n      *ngIf=\"!loading && filteredUsers.length === 0\"\r\n      class=\"bg-white dark:bg-[#1e1e1e] rounded-xl shadow-md dark:shadow-[0_4px_20px_rgba(0,0,0,0.2)] overflow-hidden mb-8 backdrop-blur-sm border border-[#edf1f4]/50 dark:border-[#2a2a2a] relative\"\r\n    >\r\n      <!-- Decorative gradient top border -->\r\n      <div\r\n        class=\"absolute top-0 left-0 right-0 h-0.5 bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad]\"\r\n      ></div>\r\n\r\n      <div class=\"p-10 text-center\">\r\n        <div class=\"relative mx-auto w-20 h-20 mb-6\">\r\n          <!-- Glow effect -->\r\n          <div\r\n            class=\"absolute inset-0 bg-[#4f5fad]/20 dark:bg-[#6d78c9]/20 rounded-full blur-xl\"\r\n          ></div>\r\n\r\n          <!-- Icon container with gradient background -->\r\n          <div\r\n            class=\"relative z-10 w-20 h-20 rounded-full bg-gradient-to-br from-[#edf1f4] to-white dark:from-[#1a1a1a] dark:to-[#2a2a2a] flex items-center justify-center shadow-lg\"\r\n          >\r\n            <i\r\n              class=\"fas fa-users text-3xl bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] bg-clip-text text-transparent\"\r\n            ></i>\r\n          </div>\r\n\r\n          <!-- Animated rings -->\r\n          <div\r\n            class=\"absolute inset-0 border-2 border-[#4f5fad]/20 dark:border-[#6d78c9]/20 rounded-full animate-ping opacity-75\"\r\n          ></div>\r\n          <div\r\n            class=\"absolute inset-0 border border-[#4f5fad]/40 dark:border-[#6d78c9]/40 rounded-full animate-pulse\"\r\n          ></div>\r\n        </div>\r\n\r\n        <h3\r\n          class=\"text-xl font-bold bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] bg-clip-text text-transparent mb-3\"\r\n        >\r\n          No users found\r\n        </h3>\r\n\r\n        <p class=\"text-sm text-[#6d6870] dark:text-[#a0a0a0] max-w-md mx-auto\">\r\n          {{\r\n            searchTerm\r\n              ? \"No users match your search criteria.\"\r\n              : \"There are no users in the system yet.\"\r\n          }}\r\n        </p>\r\n\r\n        <div class=\"mt-8\" *ngIf=\"searchTerm\">\r\n          <button\r\n            (click)=\"clearSearch()\"\r\n            class=\"inline-flex items-center px-4 py-2.5 text-sm relative overflow-hidden group\"\r\n          >\r\n            <div\r\n              class=\"absolute inset-0 bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#3d4a85] dark:to-[#6d78c9] rounded-lg opacity-10 dark:opacity-20 group-hover:opacity-20 dark:group-hover:opacity-30 transition-opacity\"\r\n            ></div>\r\n            <div\r\n              class=\"relative z-10 flex items-center text-[#4f5fad] dark:text-[#6d78c9] font-medium\"\r\n            >\r\n              <i class=\"fas fa-times-circle mr-2\"></i>\r\n              Clear search\r\n            </div>\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n  <!-- /.container-fluid -->\r\n</div>\r\n\r\n<!-- Create User Modal -->\r\n<div\r\n  *ngIf=\"showCreateUserModal\"\r\n  class=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\"\r\n  (click)=\"closeCreateUserModal()\"\r\n>\r\n  <div\r\n    class=\"bg-white dark:bg-[#1e1e1e] rounded-xl shadow-2xl max-w-md w-full max-h-[90vh] overflow-y-auto relative\"\r\n    (click)=\"$event.stopPropagation()\"\r\n  >\r\n    <!-- Modal Header -->\r\n    <div\r\n      class=\"p-6 border-b border-[#edf1f4] dark:border-[#2a2a2a] relative\"\r\n    >\r\n      <!-- Decorative gradient top border -->\r\n      <div\r\n        class=\"absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad]\"\r\n      ></div>\r\n\r\n      <div class=\"flex items-center justify-between\">\r\n        <h3\r\n          class=\"text-xl font-bold bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] bg-clip-text text-transparent\"\r\n        >\r\n          <i class=\"fas fa-user-plus mr-2\"></i>\r\n          Create New User\r\n        </h3>\r\n        <button\r\n          (click)=\"closeCreateUserModal()\"\r\n          class=\"text-[#6d6870] dark:text-[#a0a0a0] hover:text-[#ff6b69] dark:hover:text-[#ff8785] transition-colors\"\r\n        >\r\n          <i class=\"fas fa-times text-xl\"></i>\r\n        </button>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Modal Body -->\r\n    <div class=\"p-6\">\r\n      <form #createUserForm=\"ngForm\" (ngSubmit)=\"onCreateUser(createUserForm)\">\r\n        <!-- Full Name -->\r\n        <div class=\"mb-4\">\r\n          <label\r\n            for=\"fullName\"\r\n            class=\"block text-sm font-medium text-[#6d6870] dark:text-[#a0a0a0] mb-2\"\r\n          >\r\n            Full Name <span class=\"text-[#ff6b69]\">*</span>\r\n          </label>\r\n          <input\r\n            type=\"text\"\r\n            id=\"fullName\"\r\n            name=\"fullName\"\r\n            [(ngModel)]=\"newUser.fullName\"\r\n            required\r\n            class=\"w-full px-3 py-2.5 rounded-lg border border-[#bdc6cc] dark:border-[#2a2a2a] bg-white dark:bg-[#1a1a1a] text-[#2c3e50] dark:text-[#e0e0e0] focus:outline-none focus:border-[#4f5fad] dark:focus:border-[#6d78c9] focus:ring-2 focus:ring-[#4f5fad]/20 dark:focus:ring-[#6d78c9]/20 transition-all\"\r\n            placeholder=\"Enter full name\"\r\n            autocomplete=\"name\"\r\n          />\r\n        </div>\r\n\r\n        <!-- Email -->\r\n        <div class=\"mb-4\">\r\n          <label\r\n            for=\"email\"\r\n            class=\"block text-sm font-medium text-[#6d6870] dark:text-[#a0a0a0] mb-2\"\r\n          >\r\n            Email Address <span class=\"text-[#ff6b69]\">*</span>\r\n          </label>\r\n          <input\r\n            type=\"email\"\r\n            id=\"email\"\r\n            name=\"email\"\r\n            [(ngModel)]=\"newUser.email\"\r\n            required\r\n            email\r\n            class=\"w-full px-3 py-2.5 rounded-lg border border-[#bdc6cc] dark:border-[#2a2a2a] bg-white dark:bg-[#1a1a1a] text-[#2c3e50] dark:text-[#e0e0e0] focus:outline-none focus:border-[#4f5fad] dark:focus:border-[#6d78c9] focus:ring-2 focus:ring-[#4f5fad]/20 dark:focus:ring-[#6d78c9]/20 transition-all\"\r\n            placeholder=\"Enter email address\"\r\n            autocomplete=\"email\"\r\n          />\r\n        </div>\r\n\r\n        <!-- Role -->\r\n        <div class=\"mb-4\">\r\n          <label\r\n            for=\"role\"\r\n            class=\"block text-sm font-medium text-[#6d6870] dark:text-[#a0a0a0] mb-2\"\r\n          >\r\n            Role <span class=\"text-[#ff6b69]\">*</span>\r\n          </label>\r\n          <select\r\n            id=\"role\"\r\n            name=\"role\"\r\n            [(ngModel)]=\"newUser.role\"\r\n            required\r\n            class=\"w-full px-3 py-2.5 rounded-lg border border-[#bdc6cc] dark:border-[#2a2a2a] bg-white dark:bg-[#1a1a1a] text-[#2c3e50] dark:text-[#e0e0e0] focus:outline-none focus:border-[#4f5fad] dark:focus:border-[#6d78c9] focus:ring-2 focus:ring-[#4f5fad]/20 dark:focus:ring-[#6d78c9]/20 transition-all\"\r\n          >\r\n            <option value=\"student\">Student</option>\r\n            <option value=\"teacher\">Teacher</option>\r\n            <option value=\"admin\">Admin</option>\r\n          </select>\r\n        </div>\r\n\r\n        <!-- Info Box -->\r\n        <div\r\n          class=\"bg-[#e3f2fd] dark:bg-[#1a237e]/10 border border-[#2196f3]/30 dark:border-[#2196f3]/20 rounded-lg p-4 mb-6\"\r\n        >\r\n          <div class=\"flex items-start\">\r\n            <i\r\n              class=\"fas fa-info-circle text-[#2196f3] dark:text-[#64b5f6] mr-3 mt-0.5\"\r\n            ></i>\r\n            <div>\r\n              <h4\r\n                class=\"text-sm font-medium text-[#1976d2] dark:text-[#64b5f6] mb-1\"\r\n              >\r\n                Password Generation\r\n              </h4>\r\n              <p class=\"text-xs text-[#1976d2]/80 dark:text-[#64b5f6]/80\">\r\n                A secure password will be automatically generated and sent to the user's email address along with login instructions.\r\n              </p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- Form Actions -->\r\n        <div class=\"flex justify-end space-x-3\">\r\n          <button\r\n            type=\"button\"\r\n            (click)=\"closeCreateUserModal()\"\r\n            class=\"px-4 py-2.5 text-sm font-medium text-[#6d6870] dark:text-[#a0a0a0] bg-[#f8fafc] dark:bg-[#2a2a2a] border border-[#bdc6cc] dark:border-[#2a2a2a] rounded-lg hover:bg-[#edf1f4] dark:hover:bg-[#1a1a1a] transition-colors\"\r\n          >\r\n            Cancel\r\n          </button>\r\n          <button\r\n            type=\"submit\"\r\n            [disabled]=\"!createUserForm.valid || creatingUser\"\r\n            class=\"px-4 py-2.5 text-sm font-medium text-white rounded-lg relative overflow-hidden group transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed\"\r\n            [class.opacity-50]=\"!createUserForm.valid || creatingUser\"\r\n          >\r\n            <!-- Background gradient -->\r\n            <div\r\n              class=\"absolute inset-0 bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#4f5fad] dark:to-[#6d78c9] rounded-lg\"\r\n            ></div>\r\n\r\n            <!-- Content -->\r\n            <div class=\"relative z-10 flex items-center\">\r\n              <i\r\n                *ngIf=\"!creatingUser\"\r\n                class=\"fas fa-user-plus mr-2\"\r\n              ></i>\r\n              <i\r\n                *ngIf=\"creatingUser\"\r\n                class=\"fas fa-spinner fa-spin mr-2\"\r\n              ></i>\r\n              {{ creatingUser ? 'Creating...' : 'Create User' }}\r\n            </div>\r\n          </button>\r\n        </div>\r\n      </form>\r\n    </div>\r\n  </div>\r\n</div>\r\n"], "mappings": ";;;;;;;;ICoDIA,EAAA,CAAAC,cAAA,cAGC;IAGKD,EAAA,CAAAE,SAAA,YAAmC;IAKrCF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,cAAoB;IAEhBD,EAAA,CAAAI,MAAA,gBACF;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,YAA4D;IAC1DD,EAAA,CAAAI,MAAA,GACF;IAAAJ,EAAA,CAAAG,YAAA,EAAI;;;;IADFH,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAAC,MAAA,CAAAC,OAAA,MACF;;;;;IAKNR,EAAA,CAAAC,cAAA,cAGC;IAGKD,EAAA,CAAAE,SAAA,YAA2C;IAK7CF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,cAAoB;IAEhBD,EAAA,CAAAI,MAAA,cACF;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,YAA4D;IAC1DD,EAAA,CAAAI,MAAA,GACF;IAAAJ,EAAA,CAAAG,YAAA,EAAI;;;;IADFH,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAAG,MAAA,CAAAC,KAAA,MACF;;;;;IAMNV,EAAA,CAAAC,cAAA,cAAoE;IAEhED,EAAA,CAAAE,SAAA,cAEO;IAKTF,EAAA,CAAAG,YAAA,EAAM;;;;;IAIRH,EAAA,CAAAC,cAAA,cAGC;IAMGD,EAAA,CAAAE,SAAA,cAEO;IAOPF,EAAA,CAAAC,cAAA,cAAiB;IAMTD,EAAA,CAAAI,MAAA,oBACF;IAAAJ,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,cAEC;IACCD,EAAA,CAAAI,MAAA,IACF;IAAAJ,EAAA,CAAAG,YAAA,EAAM;IAERH,EAAA,CAAAC,cAAA,eAA2B;IACzBD,EAAA,CAAAE,SAAA,eAEO;IACPF,EAAA,CAAAC,cAAA,eAEC;IACCD,EAAA,CAAAE,SAAA,aAEK;IACPF,EAAA,CAAAG,YAAA,EAAM;IAGVH,EAAA,CAAAC,cAAA,eAEC;IACCD,EAAA,CAAAE,SAAA,eAEO;IACPF,EAAA,CAAAC,cAAA,eAGC;IACCD,EAAA,CAAAE,SAAA,eAEO;IACTF,EAAA,CAAAG,YAAA,EAAM;IAMZH,EAAA,CAAAC,cAAA,eAEC;IAECD,EAAA,CAAAE,SAAA,eAEO;IAOPF,EAAA,CAAAC,cAAA,eAAiB;IAMTD,EAAA,CAAAI,MAAA,qBACF;IAAAJ,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,eAAyC;IAKnCD,EAAA,CAAAE,SAAA,eAEO;IACTF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,gBACG;IAAAD,EAAA,CAAAI,MAAA,IAA6B;IAAAJ,EAAA,CAAAG,YAAA,EAC/B;IAEHH,EAAA,CAAAC,cAAA,eAA+B;IAC7BD,EAAA,CAAAE,SAAA,eAEO;IACPF,EAAA,CAAAC,cAAA,gBACG;IAAAD,EAAA,CAAAI,MAAA,IAAiC;IAAAJ,EAAA,CAAAG,YAAA,EACnC;IAIPH,EAAA,CAAAC,cAAA,eAA2B;IACzBD,EAAA,CAAAE,SAAA,eAEO;IACPF,EAAA,CAAAC,cAAA,eAEC;IACCD,EAAA,CAAAE,SAAA,aAEK;IACPF,EAAA,CAAAG,YAAA,EAAM;IAGVH,EAAA,CAAAC,cAAA,eAEC;IACCD,EAAA,CAAAE,SAAA,eAEO;IACPF,EAAA,CAAAC,cAAA,eAAuC;IAOnCD,EAAA,CAAAE,SAAA,eAEO;IACTF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,eAKC;IACCD,EAAA,CAAAE,SAAA,eAEO;IACTF,EAAA,CAAAG,YAAA,EAAM;IAOdH,EAAA,CAAAC,cAAA,eAEC;IAECD,EAAA,CAAAE,SAAA,eAEO;IAOPF,EAAA,CAAAC,cAAA,eAAiB;IAMTD,EAAA,CAAAI,MAAA,oBACF;IAAAJ,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,eAA+C;IAE3CD,EAAA,CAAAE,SAAA,eAEO;IACPF,EAAA,CAAAC,cAAA,gBACG;IAAAD,EAAA,CAAAI,MAAA,IAAgC;IAAAJ,EAAA,CAAAG,YAAA,EAClC;IAEHH,EAAA,CAAAC,cAAA,eAA+B;IAC7BD,EAAA,CAAAE,SAAA,eAEO;IACPF,EAAA,CAAAC,cAAA,gBACG;IAAAD,EAAA,CAAAI,MAAA,IAAgC;IAAAJ,EAAA,CAAAG,YAAA,EAClC;IAEHH,EAAA,CAAAC,cAAA,eAA+B;IAC7BD,EAAA,CAAAE,SAAA,eAEO;IACPF,EAAA,CAAAC,cAAA,gBACG;IAAAD,EAAA,CAAAI,MAAA,IAA4B;IAAAJ,EAAA,CAAAG,YAAA,EAC9B;IAIPH,EAAA,CAAAC,cAAA,eAA2B;IACzBD,EAAA,CAAAE,SAAA,eAEO;IACPF,EAAA,CAAAC,cAAA,eAEC;IACCD,EAAA,CAAAE,SAAA,aAEK;IACPF,EAAA,CAAAG,YAAA,EAAM;IAGVH,EAAA,CAAAC,cAAA,eAEC;IACCD,EAAA,CAAAE,SAAA,eAEO;IACPF,EAAA,CAAAC,cAAA,eAAuC;IAOnCD,EAAA,CAAAE,SAAA,eAEO;IACTF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,eAKC;IACCD,EAAA,CAAAE,SAAA,eAEO;IACTF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,eAKC;IACCD,EAAA,CAAAE,SAAA,eAEO;IACTF,EAAA,CAAAG,YAAA,EAAM;IAGVH,EAAA,CAAAC,cAAA,eAEC;IACOD,EAAA,CAAAI,MAAA,gBAAQ;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IACrBH,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAI,MAAA,gBAAQ;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IACrBH,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAI,MAAA,cAAM;IAAAJ,EAAA,CAAAG,YAAA,EAAO;;;;IAtOfH,EAAA,CAAAK,SAAA,IACF;IADEL,EAAA,CAAAM,kBAAA,MAAAK,MAAA,CAAAC,KAAA,CAAAC,MAAA,MACF;IAiEOb,EAAA,CAAAK,SAAA,IAA6B;IAA7BL,EAAA,CAAAM,kBAAA,KAAAK,MAAA,CAAAG,cAAA,cAA6B;IAQ7Bd,EAAA,CAAAK,SAAA,GAAiC;IAAjCL,EAAA,CAAAM,kBAAA,KAAAK,MAAA,CAAAI,gBAAA,gBAAiC;IA2BtCf,EAAA,CAAAK,SAAA,GAEC;IAFDL,EAAA,CAAAgB,WAAA,UAAAL,MAAA,CAAAC,KAAA,CAAAC,MAAA,GAAAF,MAAA,CAAAG,cAAA,KAAAH,MAAA,CAAAC,KAAA,CAAAC,MAAA,gBAEC;IAQDb,EAAA,CAAAK,SAAA,GAEC;IAFDL,EAAA,CAAAgB,WAAA,UAAAL,MAAA,CAAAC,KAAA,CAAAC,MAAA,GAAAF,MAAA,CAAAI,gBAAA,KAAAJ,MAAA,CAAAC,KAAA,CAAAC,MAAA,gBAEC;IAuCIb,EAAA,CAAAK,SAAA,IAAgC;IAAhCL,EAAA,CAAAM,kBAAA,KAAAK,MAAA,CAAAM,eAAA,gBAAgC;IAQhCjB,EAAA,CAAAK,SAAA,GAAgC;IAAhCL,EAAA,CAAAM,kBAAA,KAAAK,MAAA,CAAAO,eAAA,gBAAgC;IAQhClB,EAAA,CAAAK,SAAA,GAA4B;IAA5BL,EAAA,CAAAM,kBAAA,KAAAK,MAAA,CAAAQ,aAAA,cAA4B;IA2BjCnB,EAAA,CAAAK,SAAA,GAEC;IAFDL,EAAA,CAAAgB,WAAA,UAAAL,MAAA,CAAAC,KAAA,CAAAC,MAAA,GAAAF,MAAA,CAAAM,eAAA,KAAAN,MAAA,CAAAC,KAAA,CAAAC,MAAA,gBAEC;IAQDb,EAAA,CAAAK,SAAA,GAEC;IAFDL,EAAA,CAAAgB,WAAA,UAAAL,MAAA,CAAAC,KAAA,CAAAC,MAAA,GAAAF,MAAA,CAAAO,eAAA,KAAAP,MAAA,CAAAC,KAAA,CAAAC,MAAA,gBAEC;IAQDb,EAAA,CAAAK,SAAA,GAEC;IAFDL,EAAA,CAAAgB,WAAA,UAAAL,MAAA,CAAAC,KAAA,CAAAC,MAAA,GAAAF,MAAA,CAAAQ,aAAA,KAAAR,MAAA,CAAAC,KAAA,CAAAC,MAAA,gBAEC;;;;;;IAqETb,EAAA,CAAAC,cAAA,kBAIC;IAFCD,EAAA,CAAAoB,UAAA,mBAAAC,qEAAA;MAAArB,EAAA,CAAAsB,aAAA,CAAAC,IAAA;MAAA,MAAAC,MAAA,GAAAxB,EAAA,CAAAyB,aAAA;MAAA,OAASzB,EAAA,CAAA0B,WAAA,CAAAF,MAAA,CAAAG,WAAA,EAAa;IAAA,EAAC;IAGvB3B,EAAA,CAAAE,SAAA,aAAmC;IACrCF,EAAA,CAAAG,YAAA,EAAS;;;;;;IAvDbH,EAAA,CAAAC,cAAA,cAGC;IAGGD,EAAA,CAAAoB,UAAA,mBAAAQ,2DAAA;MAAA5B,EAAA,CAAAsB,aAAA,CAAAO,IAAA;MAAA,MAAAC,OAAA,GAAA9B,EAAA,CAAAyB,aAAA;MAAA,OAASzB,EAAA,CAAA0B,WAAA,CAAAI,OAAA,CAAAC,mBAAA,EAAqB;IAAA,EAAC;IAI/B/B,EAAA,CAAAE,SAAA,cAEO;IAQPF,EAAA,CAAAC,cAAA,cAAwD;IACtDD,EAAA,CAAAE,SAAA,YAAgF;IAChFF,EAAA,CAAAI,MAAA,oBACF;IAAAJ,EAAA,CAAAG,YAAA,EAAM;IAIRH,EAAA,CAAAC,cAAA,cAA2C;IAIvCD,EAAA,CAAAE,SAAA,YAEK;IACPF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,iBAME;IAHAD,EAAA,CAAAoB,UAAA,mBAAAY,2DAAAC,MAAA;MAAAjC,EAAA,CAAAsB,aAAA,CAAAO,IAAA;MAAA,MAAAK,OAAA,GAAAlC,EAAA,CAAAyB,aAAA;MAAAS,OAAA,CAAAC,UAAA,GAAAF,MAAA,CAAAG,MAAA,CAAAC,KAAA;MAAA,OAAiDrC,EAAA,CAAA0B,WAAA,CAAAQ,OAAA,CAAAI,WAAA,EAAa;IAAA,EAAC;IAHjEtC,EAAA,CAAAG,YAAA,EAME;IACFH,EAAA,CAAAC,cAAA,eAEC;IACCD,EAAA,CAAAE,SAAA,eAEO;IACTF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAuC,UAAA,KAAAC,4CAAA,qBAMS;IACXxC,EAAA,CAAAG,YAAA,EAAM;;;;IAnBFH,EAAA,CAAAK,SAAA,IAAoB;IAApBL,EAAA,CAAAyC,UAAA,UAAAC,MAAA,CAAAP,UAAA,CAAoB;IAanBnC,EAAA,CAAAK,SAAA,GAAgB;IAAhBL,EAAA,CAAAyC,UAAA,SAAAC,MAAA,CAAAP,UAAA,CAAgB;;;;;IAqKPnC,EAAA,CAAAC,cAAA,kBAAkD;IAChDD,EAAA,CAAAI,MAAA,GACF;;IAAAJ,EAAA,CAAAG,YAAA,EAAS;;;;IAF0BH,EAAA,CAAAyC,UAAA,UAAAE,QAAA,CAAc;IAC/C3C,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAAN,EAAA,CAAA4C,WAAA,OAAAD,QAAA,OACF;;;;;;IA/ER3C,EAAA,CAAAC,cAAA,cAGC;IAMOD,EAAA,CAAAE,SAAA,eAEO;IACPF,EAAA,CAAAC,cAAA,cAA4B;IAAAD,EAAA,CAAAI,MAAA,GAE1B;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAEXH,EAAA,CAAAC,cAAA,eAAkB;IAIdD,EAAA,CAAAI,MAAA,GACF;IAAAJ,EAAA,CAAAG,YAAA,EAAM;IAIZH,EAAA,CAAAC,cAAA,eAAwC;IAIpCD,EAAA,CAAAI,MAAA,IACF;IAAAJ,EAAA,CAAAG,YAAA,EAAM;IAERH,EAAA,CAAAC,cAAA,eAAoD;IAShDD,EAAA,CAAAE,SAAA,SAMK;IACLF,EAAA,CAAAI,MAAA,IACF;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAETH,EAAA,CAAAC,cAAA,eAAoD;IAShDD,EAAA,CAAAE,SAAA,SAMK;IACLF,EAAA,CAAAI,MAAA,IACF;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAETH,EAAA,CAAAC,cAAA,eAAoD;IAK9CD,EAAA,CAAAoB,UAAA,oBAAAyB,mEAAAZ,MAAA;MAAA,MAAAa,WAAA,GAAA9C,EAAA,CAAAsB,aAAA,CAAAyB,IAAA;MAAA,MAAAC,QAAA,GAAAF,WAAA,CAAAG,SAAA;MAAA,MAAAC,OAAA,GAAAlD,EAAA,CAAAyB,aAAA;MAAA,OAAUzB,EAAA,CAAA0B,WAAA,CAAAwB,OAAA,CAAAC,YAAA,CAAAH,QAAA,CAAAI,GAAA,EAAAnB,MAAA,CAAAG,MAAA,CAAAC,KAAA,CAAiD;IAAA,EAAC;IAE5DrC,EAAA,CAAAuC,UAAA,KAAAc,kDAAA,sBAES;IACXrD,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,gBAEC;IACCD,EAAA,CAAAE,SAAA,cAA2C;IAC7CF,EAAA,CAAAG,YAAA,EAAM;IAGVH,EAAA,CAAAC,cAAA,eAAoD;IAQhDD,EAAA,CAAAoB,UAAA,mBAAAkC,kEAAA;MAAA,MAAAR,WAAA,GAAA9C,EAAA,CAAAsB,aAAA,CAAAyB,IAAA;MAAA,MAAAC,QAAA,GAAAF,WAAA,CAAAG,SAAA;MAAA,MAAAM,OAAA,GAAAvD,EAAA,CAAAyB,aAAA;MAAA,OACuBzB,EAAA,CAAA0B,WAAA,CAAA6B,OAAA,CAAAC,oBAAA,CAAAR,QAAA,CAAAI,GAAA,EAAAJ,QAAA,CAAAS,QAAA,KAC5B,KAAK,CAAC;IAAA;IAQDzD,EAAA,CAAAE,SAAA,gBAMM;IAUNF,EAAA,CAAAC,cAAA,eAA4B;IAC1BD,EAAA,CAAAI,MAAA,IACF;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAGXH,EAAA,CAAAC,cAAA,eAAoD;IAGhDD,EAAA,CAAAoB,UAAA,mBAAAsC,kEAAA;MAAA,MAAAZ,WAAA,GAAA9C,EAAA,CAAAsB,aAAA,CAAAyB,IAAA;MAAA,MAAAC,QAAA,GAAAF,WAAA,CAAAG,SAAA;MAAA,MAAAU,OAAA,GAAA3D,EAAA,CAAAyB,aAAA;MAAA,OAASzB,EAAA,CAAA0B,WAAA,CAAAiC,OAAA,CAAAC,YAAA,CAAAZ,QAAA,CAAAI,GAAA,CAAsB;IAAA,EAAC;IAGhCpD,EAAA,CAAAE,SAAA,cAAuC;IACvCF,EAAA,CAAAI,MAAA,gBACF;IAAAJ,EAAA,CAAAG,YAAA,EAAS;IAEXH,EAAA,CAAAC,cAAA,eAAoD;IAGhDD,EAAA,CAAAoB,UAAA,mBAAAyC,kEAAA;MAAA,MAAAf,WAAA,GAAA9C,EAAA,CAAAsB,aAAA,CAAAyB,IAAA;MAAA,MAAAC,QAAA,GAAAF,WAAA,CAAAG,SAAA;MAAA,MAAAa,OAAA,GAAA9D,EAAA,CAAAyB,aAAA;MAAA,OAASzB,EAAA,CAAA0B,WAAA,CAAAoC,OAAA,CAAAC,eAAA,CAAAf,QAAA,CAAAI,GAAA,CAAyB;IAAA,EAAC;IAEnCpD,EAAA,CAAAE,SAAA,cAAiC;IACjCF,EAAA,CAAAI,MAAA,iBACF;IAAAJ,EAAA,CAAAG,YAAA,EAAS;;;;;IApIuBH,EAAA,CAAAK,SAAA,GAE1B;IAF0BL,EAAA,CAAAgE,iBAAA,CAAAhB,QAAA,CAAAiB,QAAA,CAAAC,MAAA,IAE1B;IAMAlE,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAA0C,QAAA,CAAAiB,QAAA,MACF;IAQFjE,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAA0C,QAAA,CAAAmB,KAAA,MACF;IAKEnE,EAAA,CAAAK,SAAA,GAIC;IAJDL,EAAA,CAAAyC,UAAA,YAAAO,QAAA,CAAAoB,QAAA,uJAIC;IAGCpE,EAAA,CAAAK,SAAA,GAIC;IAJDL,EAAA,CAAAqE,UAAA,CAAArB,QAAA,CAAAoB,QAAA,2DAIC;IAEHpE,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAA0C,QAAA,CAAAoB,QAAA,qBACF;IAKEpE,EAAA,CAAAK,SAAA,GAIC;IAJDL,EAAA,CAAAyC,UAAA,YAAAO,QAAA,CAAAS,QAAA,6OAIC;IAGCzD,EAAA,CAAAK,SAAA,GAIC;IAJDL,EAAA,CAAAqE,UAAA,CAAArB,QAAA,CAAAS,QAAA,iGAIC;IAEHzD,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAA0C,QAAA,CAAAS,QAAA,2CACF;IAMIzD,EAAA,CAAAK,SAAA,GAAmB;IAAnBL,EAAA,CAAAyC,UAAA,UAAAO,QAAA,CAAAsB,IAAA,CAAmB;IAGMtE,EAAA,CAAAK,SAAA,GAAQ;IAARL,EAAA,CAAAyC,UAAA,YAAA8B,OAAA,CAAAC,KAAA,CAAQ;IAcnCxE,EAAA,CAAAK,SAAA,GAIC;IAJDL,EAAA,CAAAyC,UAAA,YAAAO,QAAA,CAAAS,QAAA,+VAIC,UAAAT,QAAA,CAAAS,QAAA;IAYIzD,EAAA,CAAAK,SAAA,GAIC;IAJDL,EAAA,CAAAyC,UAAA,YAAAO,QAAA,CAAAS,QAAA,uHAIC;IAKJzD,EAAA,CAAAK,SAAA,GAIC;IAJDL,EAAA,CAAAqE,UAAA,CAAArB,QAAA,CAAAS,QAAA,uDAIC;IAGDzD,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAA0C,QAAA,CAAAS,QAAA,4CACF;;;;;IA1MdzD,EAAA,CAAAC,cAAA,eAGC;IAECD,EAAA,CAAAE,SAAA,eAEO;IAEPF,EAAA,CAAAC,cAAA,eAEC;IAIGD,EAAA,CAAAE,SAAA,aAAqC;IACrCF,EAAA,CAAAI,MAAA,wBACF;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IAEPH,EAAA,CAAAC,cAAA,eAA6B;IAUnBD,EAAA,CAAAI,MAAA,cACF;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,eAGC;IACCD,EAAA,CAAAI,MAAA,eACF;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,eAGC;IACCD,EAAA,CAAAI,MAAA,kBACF;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,eAGC;IACCD,EAAA,CAAAI,MAAA,gBACF;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,eAGC;IACCD,EAAA,CAAAI,MAAA,cACF;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,eAGC;IACCD,EAAA,CAAAI,MAAA,kBACF;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,eAGC;IACCD,EAAA,CAAAI,MAAA,gBACF;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,eAGC;IACCD,EAAA,CAAAI,MAAA,iBACF;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IAGTH,EAAA,CAAAC,cAAA,kBAEC;IACCD,EAAA,CAAAuC,UAAA,KAAAkC,wCAAA,oBAkJK;IACPzE,EAAA,CAAAG,YAAA,EAAQ;;;;IAlJaH,EAAA,CAAAK,SAAA,IAAgB;IAAhBL,EAAA,CAAAyC,UAAA,YAAAiC,MAAA,CAAAC,aAAA,CAAgB;;;;;;IAwMvC3E,EAAA,CAAAC,cAAA,eAAqC;IAEjCD,EAAA,CAAAoB,UAAA,mBAAAwD,kEAAA;MAAA5E,EAAA,CAAAsB,aAAA,CAAAuD,IAAA;MAAA,MAAAC,OAAA,GAAA9E,EAAA,CAAAyB,aAAA;MAAA,OAASzB,EAAA,CAAA0B,WAAA,CAAAoD,OAAA,CAAAnD,WAAA,EAAa;IAAA,EAAC;IAGvB3B,EAAA,CAAAE,SAAA,eAEO;IACPF,EAAA,CAAAC,cAAA,eAEC;IACCD,EAAA,CAAAE,SAAA,aAAwC;IACxCF,EAAA,CAAAI,MAAA,qBACF;IAAAJ,EAAA,CAAAG,YAAA,EAAM;;;;;IA7DdH,EAAA,CAAAC,cAAA,eAGC;IAECD,EAAA,CAAAE,SAAA,eAEO;IAEPF,EAAA,CAAAC,cAAA,eAA8B;IAG1BD,EAAA,CAAAE,SAAA,eAEO;IAGPF,EAAA,CAAAC,cAAA,eAEC;IACCD,EAAA,CAAAE,SAAA,aAEK;IACPF,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAE,SAAA,eAEO;IAITF,EAAA,CAAAG,YAAA,EAAM;IAENH,EAAA,CAAAC,cAAA,cAEC;IACCD,EAAA,CAAAI,MAAA,wBACF;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IAELH,EAAA,CAAAC,cAAA,cAAuE;IACrED,EAAA,CAAAI,MAAA,IAKF;IAAAJ,EAAA,CAAAG,YAAA,EAAI;IAEJH,EAAA,CAAAuC,UAAA,KAAAwC,yCAAA,mBAeM;IACR/E,EAAA,CAAAG,YAAA,EAAM;;;;IAvBFH,EAAA,CAAAK,SAAA,IAKF;IALEL,EAAA,CAAAM,kBAAA,MAAA0E,MAAA,CAAA7C,UAAA,yFAKF;IAEmBnC,EAAA,CAAAK,SAAA,GAAgB;IAAhBL,EAAA,CAAAyC,UAAA,SAAAuC,MAAA,CAAA7C,UAAA,CAAgB;;;;;IAqK7BnC,EAAA,CAAAE,SAAA,aAGK;;;;;IACLF,EAAA,CAAAE,SAAA,aAGK;;;;;;IArJnBF,EAAA,CAAAC,cAAA,eAIC;IADCD,EAAA,CAAAoB,UAAA,mBAAA6D,wDAAA;MAAAjF,EAAA,CAAAsB,aAAA,CAAA4D,IAAA;MAAA,MAAAC,OAAA,GAAAnF,EAAA,CAAAyB,aAAA;MAAA,OAASzB,EAAA,CAAA0B,WAAA,CAAAyD,OAAA,CAAAC,oBAAA,EAAsB;IAAA,EAAC;IAEhCpF,EAAA,CAAAC,cAAA,eAGC;IADCD,EAAA,CAAAoB,UAAA,mBAAAiE,wDAAApD,MAAA;MAAA,OAASA,MAAA,CAAAqD,eAAA,EAAwB;IAAA,EAAC;IAGlCtF,EAAA,CAAAC,cAAA,eAEC;IAECD,EAAA,CAAAE,SAAA,cAEO;IAEPF,EAAA,CAAAC,cAAA,eAA+C;IAI3CD,EAAA,CAAAE,SAAA,aAAqC;IACrCF,EAAA,CAAAI,MAAA,wBACF;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,kBAGC;IAFCD,EAAA,CAAAoB,UAAA,mBAAAmE,2DAAA;MAAAvF,EAAA,CAAAsB,aAAA,CAAA4D,IAAA;MAAA,MAAAM,OAAA,GAAAxF,EAAA,CAAAyB,aAAA;MAAA,OAASzB,EAAA,CAAA0B,WAAA,CAAA8D,OAAA,CAAAJ,oBAAA,EAAsB;IAAA,EAAC;IAGhCpF,EAAA,CAAAE,SAAA,aAAoC;IACtCF,EAAA,CAAAG,YAAA,EAAS;IAKbH,EAAA,CAAAC,cAAA,eAAiB;IACgBD,EAAA,CAAAoB,UAAA,sBAAAqE,6DAAA;MAAAzF,EAAA,CAAAsB,aAAA,CAAA4D,IAAA;MAAA,MAAAQ,IAAA,GAAA1F,EAAA,CAAA2F,WAAA;MAAA,MAAAC,OAAA,GAAA5F,EAAA,CAAAyB,aAAA;MAAA,OAAYzB,EAAA,CAAA0B,WAAA,CAAAkE,OAAA,CAAAC,YAAA,CAAAH,IAAA,CAA4B;IAAA,EAAC;IAEtE1F,EAAA,CAAAC,cAAA,gBAAkB;IAKdD,EAAA,CAAAI,MAAA,mBAAU;IAAAJ,EAAA,CAAAC,cAAA,iBAA6B;IAAAD,EAAA,CAAAI,MAAA,SAAC;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAEjDH,EAAA,CAAAC,cAAA,kBASE;IALAD,EAAA,CAAAoB,UAAA,2BAAA0E,mEAAA7D,MAAA;MAAAjC,EAAA,CAAAsB,aAAA,CAAA4D,IAAA;MAAA,MAAAa,OAAA,GAAA/F,EAAA,CAAAyB,aAAA;MAAA,OAAazB,EAAA,CAAA0B,WAAA,CAAAqE,OAAA,CAAAC,OAAA,CAAA/B,QAAA,GAAAhC,MAAA,CACpB;IAAA,EADqC;IAJhCjC,EAAA,CAAAG,YAAA,EASE;IAIJH,EAAA,CAAAC,cAAA,gBAAkB;IAKdD,EAAA,CAAAI,MAAA,uBAAc;IAAAJ,EAAA,CAAAC,cAAA,iBAA6B;IAAAD,EAAA,CAAAI,MAAA,SAAC;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAErDH,EAAA,CAAAC,cAAA,kBAUE;IANAD,EAAA,CAAAoB,UAAA,2BAAA6E,mEAAAhE,MAAA;MAAAjC,EAAA,CAAAsB,aAAA,CAAA4D,IAAA;MAAA,MAAAgB,OAAA,GAAAlG,EAAA,CAAAyB,aAAA;MAAA,OAAazB,EAAA,CAAA0B,WAAA,CAAAwE,OAAA,CAAAF,OAAA,CAAA7B,KAAA,GAAAlC,MAAA,CACpB;IAAA,EADkC;IAJ7BjC,EAAA,CAAAG,YAAA,EAUE;IAIJH,EAAA,CAAAC,cAAA,gBAAkB;IAKdD,EAAA,CAAAI,MAAA,cAAK;IAAAJ,EAAA,CAAAC,cAAA,iBAA6B;IAAAD,EAAA,CAAAI,MAAA,SAAC;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAE5CH,EAAA,CAAAC,cAAA,mBAMC;IAHCD,EAAA,CAAAoB,UAAA,2BAAA+E,oEAAAlE,MAAA;MAAAjC,EAAA,CAAAsB,aAAA,CAAA4D,IAAA;MAAA,MAAAkB,OAAA,GAAApG,EAAA,CAAAyB,aAAA;MAAA,OAAazB,EAAA,CAAA0B,WAAA,CAAA0E,OAAA,CAAAJ,OAAA,CAAA1B,IAAA,GAAArC,MAAA,CACpB;IAAA,EADiC;IAI1BjC,EAAA,CAAAC,cAAA,mBAAwB;IAAAD,EAAA,CAAAI,MAAA,eAAO;IAAAJ,EAAA,CAAAG,YAAA,EAAS;IACxCH,EAAA,CAAAC,cAAA,mBAAwB;IAAAD,EAAA,CAAAI,MAAA,eAAO;IAAAJ,EAAA,CAAAG,YAAA,EAAS;IACxCH,EAAA,CAAAC,cAAA,mBAAsB;IAAAD,EAAA,CAAAI,MAAA,aAAK;IAAAJ,EAAA,CAAAG,YAAA,EAAS;IAKxCH,EAAA,CAAAC,cAAA,gBAEC;IAEGD,EAAA,CAAAE,SAAA,cAEK;IACLF,EAAA,CAAAC,cAAA,WAAK;IAIDD,EAAA,CAAAI,MAAA,6BACF;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,cAA4D;IAC1DD,EAAA,CAAAI,MAAA,+HACF;IAAAJ,EAAA,CAAAG,YAAA,EAAI;IAMVH,EAAA,CAAAC,cAAA,gBAAwC;IAGpCD,EAAA,CAAAoB,UAAA,mBAAAiF,4DAAA;MAAArG,EAAA,CAAAsB,aAAA,CAAA4D,IAAA;MAAA,MAAAoB,OAAA,GAAAtG,EAAA,CAAAyB,aAAA;MAAA,OAASzB,EAAA,CAAA0B,WAAA,CAAA4E,OAAA,CAAAlB,oBAAA,EAAsB;IAAA,EAAC;IAGhCpF,EAAA,CAAAI,MAAA,gBACF;IAAAJ,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,mBAKC;IAECD,EAAA,CAAAE,SAAA,gBAEO;IAGPF,EAAA,CAAAC,cAAA,gBAA6C;IAC3CD,EAAA,CAAAuC,UAAA,KAAAgE,uCAAA,iBAGK;IACLvG,EAAA,CAAAuC,UAAA,KAAAiE,uCAAA,iBAGK;IACLxG,EAAA,CAAAI,MAAA,IACF;IAAAJ,EAAA,CAAAG,YAAA,EAAM;;;;;IAtGNH,EAAA,CAAAK,SAAA,IAA8B;IAA9BL,EAAA,CAAAyC,UAAA,YAAAgE,MAAA,CAAAT,OAAA,CAAA/B,QAAA,CAA8B;IAoB9BjE,EAAA,CAAAK,SAAA,GAA2B;IAA3BL,EAAA,CAAAyC,UAAA,YAAAgE,MAAA,CAAAT,OAAA,CAAA7B,KAAA,CAA2B;IAoB3BnE,EAAA,CAAAK,SAAA,GAA0B;IAA1BL,EAAA,CAAAyC,UAAA,YAAAgE,MAAA,CAAAT,OAAA,CAAA1B,IAAA,CAA0B;IA4C1BtE,EAAA,CAAAK,SAAA,IAA0D;IAA1DL,EAAA,CAAA0G,WAAA,gBAAAhB,IAAA,CAAAiB,KAAA,IAAAF,MAAA,CAAAG,YAAA,CAA0D;IAF1D5G,EAAA,CAAAyC,UAAA,cAAAiD,IAAA,CAAAiB,KAAA,IAAAF,MAAA,CAAAG,YAAA,CAAkD;IAY7C5G,EAAA,CAAAK,SAAA,GAAmB;IAAnBL,EAAA,CAAAyC,UAAA,UAAAgE,MAAA,CAAAG,YAAA,CAAmB;IAInB5G,EAAA,CAAAK,SAAA,GAAkB;IAAlBL,EAAA,CAAAyC,UAAA,SAAAgE,MAAA,CAAAG,YAAA,CAAkB;IAGrB5G,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAAmG,MAAA,CAAAG,YAAA,sCACF;;;ADj3BZ,OAAM,MAAOC,kBAAkB;EAmB7BC,YACUC,WAAwB,EACxBC,MAAc,EACdC,YAA0B;IAF1B,KAAAF,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,YAAY,GAAZA,YAAY;IArBtB,KAAArG,KAAK,GAAU,EAAE;IACjB,KAAAF,KAAK,GAAG,EAAE;IACV,KAAAF,OAAO,GAAG,EAAE;IACZ,KAAAgE,KAAK,GAAG,CAAC,SAAS,EAAE,SAAS,EAAE,OAAO,CAAC;IACvC,KAAA0C,OAAO,GAAG,IAAI;IACd,KAAAC,WAAW,GAAQ,IAAI;IACvB,KAAAhF,UAAU,GAAG,EAAE;IACf,KAAAwC,aAAa,GAAU,EAAE;IAEzB;IACA,KAAAyC,mBAAmB,GAAG,KAAK;IAC3B,KAAAR,YAAY,GAAG,KAAK;IACpB,KAAAZ,OAAO,GAAG;MACR/B,QAAQ,EAAE,EAAE;MACZE,KAAK,EAAE,EAAE;MACTG,IAAI,EAAE;KACP;EAME;EAEH+C,QAAQA,CAAA;IACN,IAAI,CAACC,YAAY,EAAE;EACrB;EAEAA,YAAYA,CAAA;IACV,IAAI,CAACJ,OAAO,GAAG,IAAI;IACnB,MAAMK,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAC3C,MAAMC,OAAO,GAAGF,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC;IAE5C,IAAI,CAACF,KAAK,IAAI,CAACG,OAAO,EAAE;MACtB,IAAI,CAACV,MAAM,CAACW,QAAQ,CAAC,CAAC,cAAc,CAAC,CAAC;MACtC;;IAGF,IAAI,CAACR,WAAW,GAAGS,IAAI,CAACC,KAAK,CAACH,OAAO,CAAC;IAEtC;IACA,IAAI,IAAI,CAACP,WAAW,CAAC7C,IAAI,KAAK,OAAO,EAAE;MACrC,IAAI,CAAC0C,MAAM,CAACW,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC;MAC3B;;IAGF,IAAI,CAACZ,WAAW,CAACe,WAAW,CAACP,KAAK,CAAC,CAACQ,SAAS,CAAC;MAC5CC,IAAI,EAAGC,GAAQ,IAAI;QACjB,IAAI,CAACrH,KAAK,GAAGqH,GAAG;QAChB,IAAI,CAACtD,aAAa,GAAG,CAAC,GAAG,IAAI,CAAC/D,KAAK,CAAC;QACpC,IAAI,CAACsG,OAAO,GAAG,KAAK;MACtB,CAAC;MACDxG,KAAK,EAAGwH,GAAG,IAAI;QACb,IAAI,CAACxH,KAAK,GAAGwH,GAAG,CAACxH,KAAK,EAAEF,OAAO,IAAI,uBAAuB;QAC1D,IAAI,CAAC0G,OAAO,GAAG,KAAK;MACtB;KACD,CAAC;EACJ;EACA5E,WAAWA,CAAA;IACT,IAAI,CAAC,IAAI,CAACH,UAAU,CAACgG,IAAI,EAAE,EAAE;MAC3B,IAAI,CAACxD,aAAa,GAAG,CAAC,GAAG,IAAI,CAAC/D,KAAK,CAAC;MACpC;;IAGF,MAAMwH,IAAI,GAAG,IAAI,CAACjG,UAAU,CAACkG,WAAW,EAAE,CAACF,IAAI,EAAE;IACjD,IAAI,CAACxD,aAAa,GAAG,IAAI,CAAC/D,KAAK,CAAC0H,MAAM,CACnCC,IAAI,IACHA,IAAI,CAACtE,QAAQ,CAACoE,WAAW,EAAE,CAACG,QAAQ,CAACJ,IAAI,CAAC,IAC1CG,IAAI,CAACpE,KAAK,CAACkE,WAAW,EAAE,CAACG,QAAQ,CAACJ,IAAI,CAAC,IACvCG,IAAI,CAACjE,IAAI,CAAC+D,WAAW,EAAE,CAACG,QAAQ,CAACJ,IAAI,CAAC,CACzC;EACH;EACAzG,WAAWA,CAAA;IACT,IAAI,CAACQ,UAAU,GAAG,EAAE;IACpB,IAAI,CAACwC,aAAa,GAAG,CAAC,GAAG,IAAI,CAAC/D,KAAK,CAAC;EACtC;EAEAuC,YAAYA,CAACsF,MAAc,EAAEC,OAAe;IAC1C,MAAMnB,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAC3C,IAAI,CAACV,WAAW,CAAC4B,cAAc,CAACF,MAAM,EAAEC,OAAO,EAAEnB,KAAM,CAAC,CAACQ,SAAS,CAAC;MACjEC,IAAI,EAAGC,GAAQ,IAAI;QACjB,IAAI,CAACzH,OAAO,GAAGyH,GAAG,CAACzH,OAAO;QAC1B,IAAI,CAACE,KAAK,GAAG,EAAE;QAEf;QACA,MAAMkI,SAAS,GAAG,IAAI,CAAChI,KAAK,CAACiI,SAAS,CAAEC,CAAC,IAAKA,CAAC,CAAC1F,GAAG,KAAKqF,MAAM,CAAC;QAC/D,IAAIG,SAAS,KAAK,CAAC,CAAC,EAAE;UACpB,IAAI,CAAChI,KAAK,CAACgI,SAAS,CAAC,CAACtE,IAAI,GAAGoE,OAAO;;QAGtC,MAAMK,aAAa,GAAG,IAAI,CAACpE,aAAa,CAACkE,SAAS,CAC/CC,CAAC,IAAKA,CAAC,CAAC1F,GAAG,KAAKqF,MAAM,CACxB;QACD,IAAIM,aAAa,KAAK,CAAC,CAAC,EAAE;UACxB,IAAI,CAACpE,aAAa,CAACoE,aAAa,CAAC,CAACzE,IAAI,GAAGoE,OAAO;;QAGlD;QACAM,UAAU,CAAC,MAAK;UACd,IAAI,CAACxI,OAAO,GAAG,EAAE;QACnB,CAAC,EAAE,IAAI,CAAC;MACV,CAAC;MACDE,KAAK,EAAGwH,GAAG,IAAI;QACb,IAAI,CAACxH,KAAK,GAAGwH,GAAG,CAACxH,KAAK,EAAEF,OAAO,IAAI,uBAAuB;QAC1D,IAAI,CAACA,OAAO,GAAG,EAAE;QAEjB;QACAwI,UAAU,CAAC,MAAK;UACd,IAAI,CAACtI,KAAK,GAAG,EAAE;QACjB,CAAC,EAAE,IAAI,CAAC;MACV;KACD,CAAC;EACJ;EACAkD,YAAYA,CAAC6E,MAAc;IACzB,MAAMQ,aAAa,GAAGC,OAAO,CAAC,4CAA4C,CAAC;IAC3E,IAAI,CAACD,aAAa,EAAE;IAEpB,MAAM1B,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAC3C,IAAI,CAACV,WAAW,CAACoC,UAAU,CAACV,MAAM,EAAElB,KAAM,CAAC,CAACQ,SAAS,CAAC;MACpDC,IAAI,EAAGC,GAAQ,IAAI;QACjB,IAAI,CAACzH,OAAO,GAAGyH,GAAG,CAACzH,OAAO;QAC1B,IAAI,CAACE,KAAK,GAAG,EAAE;QAEf;QACA,IAAI,CAACE,KAAK,GAAG,IAAI,CAACA,KAAK,CAAC0H,MAAM,CAAEQ,CAAC,IAAKA,CAAC,CAAC1F,GAAG,KAAKqF,MAAM,CAAC;QACvD,IAAI,CAAC9D,aAAa,GAAG,IAAI,CAACA,aAAa,CAAC2D,MAAM,CAAEQ,CAAC,IAAKA,CAAC,CAAC1F,GAAG,KAAKqF,MAAM,CAAC;QAEvE;QACAO,UAAU,CAAC,MAAK;UACd,IAAI,CAACxI,OAAO,GAAG,EAAE;QACnB,CAAC,EAAE,IAAI,CAAC;MACV,CAAC;MACDE,KAAK,EAAGwH,GAAG,IAAI;QACb,IAAI,CAACxH,KAAK,GAAGwH,GAAG,CAACxH,KAAK,EAAEF,OAAO,IAAI,uBAAuB;QAC1D,IAAI,CAACA,OAAO,GAAG,EAAE;QAEjB;QACAwI,UAAU,CAAC,MAAK;UACd,IAAI,CAACtI,KAAK,GAAG,EAAE;QACjB,CAAC,EAAE,IAAI,CAAC;MACV;KACD,CAAC;EACJ;EACA8C,oBAAoBA,CAACiF,MAAc,EAAEW,aAAsB;IACzD,MAAMC,SAAS,GAAG,CAACD,aAAa;IAChC,MAAME,MAAM,GAAGD,SAAS,GAAG,UAAU,GAAG,YAAY;IAEpD;IACA,MAAMd,IAAI,GAAG,IAAI,CAAC3H,KAAK,CAAC2I,IAAI,CAACT,CAAC,IAAIA,CAAC,CAAC1F,GAAG,KAAKqF,MAAM,CAAC;IACnD,MAAMe,QAAQ,GAAGjB,IAAI,EAAEtE,QAAQ,IAAIsE,IAAI,EAAEkB,SAAS,IAAI,MAAM;IAE5D,MAAMC,aAAa,GAAGR,OAAO,CAC3B,4BAA4BI,MAAM,IAAIE,QAAQ,GAAG,CAClD;IACD,IAAI,CAACE,aAAa,EAAE;IAEpB,MAAMnC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAC3C,IAAI,CAACV,WAAW,CAACvD,oBAAoB,CAACiF,MAAM,EAAEY,SAAS,EAAE9B,KAAM,CAAC,CAACQ,SAAS,CAAC;MACzEC,IAAI,EAAGC,GAAQ,IAAI;QACjB,MAAM0B,UAAU,GAAGN,SAAS,GAAG,WAAW,GAAG,aAAa;QAC1D,MAAMO,cAAc,GAAG,GAAGJ,QAAQ,aAAaG,UAAU,eAAe;QAExE;QACA,IAAI,CAAC1C,YAAY,CAAC4C,WAAW,CAACD,cAAc,CAAC;QAE7C;QACA,IAAI,CAACpJ,OAAO,GAAG,EAAE;QACjB,IAAI,CAACE,KAAK,GAAG,EAAE;QAEf;QACA,MAAMkI,SAAS,GAAG,IAAI,CAAChI,KAAK,CAACiI,SAAS,CAAEC,CAAC,IAAKA,CAAC,CAAC1F,GAAG,KAAKqF,MAAM,CAAC;QAC/D,IAAIG,SAAS,KAAK,CAAC,CAAC,EAAE;UACpB,IAAI,CAAChI,KAAK,CAACgI,SAAS,CAAC,CAACnF,QAAQ,GAAG4F,SAAS;;QAG5C,MAAMN,aAAa,GAAG,IAAI,CAACpE,aAAa,CAACkE,SAAS,CAC/CC,CAAC,IAAKA,CAAC,CAAC1F,GAAG,KAAKqF,MAAM,CACxB;QACD,IAAIM,aAAa,KAAK,CAAC,CAAC,EAAE;UACxB,IAAI,CAACpE,aAAa,CAACoE,aAAa,CAAC,CAACtF,QAAQ,GAAG4F,SAAS;;QAGxD;QACA,IAAI,CAACS,YAAY,EAAE;MACrB,CAAC;MACDpJ,KAAK,EAAGwH,GAAG,IAAI;QACb,MAAMyB,UAAU,GAAGN,SAAS,GAAG,UAAU,GAAG,YAAY;QACxD,MAAMU,YAAY,GAAG7B,GAAG,CAACxH,KAAK,EAAEF,OAAO,IAAI,aAAamJ,UAAU,IAAIH,QAAQ,EAAE;QAEhF;QACA,IAAI,CAACvC,YAAY,CAAC+C,SAAS,CAACD,YAAY,CAAC;QAEzC;QACA,IAAI,CAACvJ,OAAO,GAAG,EAAE;QACjB,IAAI,CAACE,KAAK,GAAG,EAAE;MACjB;KACD,CAAC;EACJ;EACAO,eAAeA,CAAA;IACb,OAAO,IAAI,CAACL,KAAK,CAAC0H,MAAM,CAAEQ,CAAC,IAAKA,CAAC,CAACxE,IAAI,KAAK,SAAS,CAAC,CAACzD,MAAM;EAC9D;EACAK,eAAeA,CAAA;IACb,OAAO,IAAI,CAACN,KAAK,CAAC0H,MAAM,CAAEQ,CAAC,IAAKA,CAAC,CAACxE,IAAI,KAAK,SAAS,CAAC,CAACzD,MAAM;EAC9D;EACAM,aAAaA,CAAA;IACX,OAAO,IAAI,CAACP,KAAK,CAAC0H,MAAM,CAAEQ,CAAC,IAAKA,CAAC,CAACxE,IAAI,KAAK,OAAO,CAAC,CAACzD,MAAM;EAC5D;EACAC,cAAcA,CAAA;IACZ,OAAO,IAAI,CAACF,KAAK,CAAC0H,MAAM,CAAEQ,CAAC,IAAKA,CAAC,CAACrF,QAAQ,KAAK,KAAK,CAAC,CAAC5C,MAAM;EAC9D;EACAE,gBAAgBA,CAAA;IACd,OAAO,IAAI,CAACH,KAAK,CAAC0H,MAAM,CAAEQ,CAAC,IAAKA,CAAC,CAACrF,QAAQ,KAAK,KAAK,CAAC,CAAC5C,MAAM;EAC9D;EAEAoJ,MAAMA,CAAA;IACJ,IAAI,CAAClD,WAAW,CAACkD,MAAM,EAAE;IACzB,IAAI,CAACjD,MAAM,CAACW,QAAQ,CAAC,CAAC,cAAc,CAAC,CAAC;EACxC;EAEA5D,eAAeA,CAAC0E,MAAc;IAC5B,IAAI,CAACzB,MAAM,CAACW,QAAQ,CAAC,CAAC,oBAAoB,EAAEc,MAAM,CAAC,CAAC;EACtD;EAEAqB,YAAYA,CAAA;IACV,IAAI,CAACxH,WAAW,EAAE;EACpB;EAEA;EACAP,mBAAmBA,CAAA;IACjB,IAAI,CAACqF,mBAAmB,GAAG,IAAI;IAC/B,IAAI,CAAC8C,gBAAgB,EAAE;EACzB;EAEA9E,oBAAoBA,CAAA;IAClB,IAAI,CAACgC,mBAAmB,GAAG,KAAK;IAChC,IAAI,CAAC8C,gBAAgB,EAAE;EACzB;EAEAA,gBAAgBA,CAAA;IACd,IAAI,CAAClE,OAAO,GAAG;MACb/B,QAAQ,EAAE,EAAE;MACZE,KAAK,EAAE,EAAE;MACTG,IAAI,EAAE;KACP;IACD,IAAI,CAACsC,YAAY,GAAG,KAAK;EAC3B;EAEAf,YAAYA,CAACsE,IAAS;IACpB,IAAIA,IAAI,CAACC,OAAO,IAAI,IAAI,CAACxD,YAAY,EAAE;MACrC;;IAGF,IAAI,CAACA,YAAY,GAAG,IAAI;IACxB,IAAI,CAAClG,KAAK,GAAG,EAAE;IACf,IAAI,CAACF,OAAO,GAAG,EAAE;IAEjB,MAAM+G,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAE3C,IAAI,CAACV,WAAW,CAACsD,UAAU,CAAC,IAAI,CAACrE,OAAO,EAAEuB,KAAM,CAAC,CAACQ,SAAS,CAAC;MAC1DC,IAAI,EAAGC,GAAQ,IAAI;QACjB;QACA,IAAI,CAACzH,OAAO,GAAGyH,GAAG,CAACzH,OAAO;QAC1B,IAAI,CAACE,KAAK,GAAG,EAAE;QAEf;QACA,IAAI,CAACE,KAAK,CAAC0J,IAAI,CAACrC,GAAG,CAACM,IAAI,CAAC;QACzB,IAAI,CAAC5D,aAAa,GAAG,CAAC,GAAG,IAAI,CAAC/D,KAAK,CAAC;QACpC,IAAI,CAACkJ,YAAY,EAAE;QAEnB;QACA,IAAI,CAAC7C,YAAY,CAAC4C,WAAW,CAC3B,wDAAwD5B,GAAG,CAACM,IAAI,CAACpE,KAAK,EAAE,CACzE;QAED;QACA,IAAI,CAACiB,oBAAoB,EAAE;QAE3B;QACA4D,UAAU,CAAC,MAAK;UACd,IAAI,CAACxI,OAAO,GAAG,EAAE;QACnB,CAAC,EAAE,IAAI,CAAC;MACV,CAAC;MACDE,KAAK,EAAGwH,GAAG,IAAI;QACb,IAAI,CAACxH,KAAK,GAAGwH,GAAG,CAACxH,KAAK,EAAEF,OAAO,IAAI,uBAAuB;QAC1D,IAAI,CAACA,OAAO,GAAG,EAAE;QACjB,IAAI,CAACoG,YAAY,GAAG,KAAK;QAEzB;QACA,IAAI,CAACK,YAAY,CAAC+C,SAAS,CAAC,IAAI,CAACtJ,KAAK,CAAC;QAEvC;QACAsI,UAAU,CAAC,MAAK;UACd,IAAI,CAACtI,KAAK,GAAG,EAAE;QACjB,CAAC,EAAE,IAAI,CAAC;MACV;KACD,CAAC;EACJ;;;uBAzSWmG,kBAAkB,EAAA7G,EAAA,CAAAuK,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAzK,EAAA,CAAAuK,iBAAA,CAAAG,EAAA,CAAAC,MAAA,GAAA3K,EAAA,CAAAuK,iBAAA,CAAAK,EAAA,CAAAC,YAAA;IAAA;EAAA;;;YAAlBhE,kBAAkB;MAAAiE,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,4BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCT/BpL,EAAA,CAAAC,cAAA,aAEC;UAGGD,EAAA,CAAAE,SAAA,aAEO;UAMPF,EAAA,CAAAC,cAAA,aAA4D;UAExDD,EAAA,CAAAE,SAAA,aAAmE;UAWrEF,EAAA,CAAAG,YAAA,EAAM;UAKVH,EAAA,CAAAC,cAAA,cAA2B;UASnBD,EAAA,CAAAI,MAAA,yBACF;UAAAJ,EAAA,CAAAG,YAAA,EAAK;UACLH,EAAA,CAAAC,cAAA,aAAsD;UACpDD,EAAA,CAAAI,MAAA,0CACF;UAAAJ,EAAA,CAAAG,YAAA,EAAI;UAORH,EAAA,CAAAuC,UAAA,KAAA+I,kCAAA,mBAqBM;UAENtL,EAAA,CAAAuC,UAAA,KAAAgJ,kCAAA,mBAqBM;UAGNvL,EAAA,CAAAuC,UAAA,KAAAiJ,kCAAA,kBAUM;UAGNxL,EAAA,CAAAuC,UAAA,KAAAkJ,kCAAA,oBAuQM;UAGNzL,EAAA,CAAAuC,UAAA,KAAAmJ,kCAAA,mBAyDM;UAGN1L,EAAA,CAAAuC,UAAA,KAAAoJ,kCAAA,mBAoOM;UAGN3L,EAAA,CAAAuC,UAAA,KAAAqJ,kCAAA,mBAiEM;UACR5L,EAAA,CAAAG,YAAA,EAAM;UAKRH,EAAA,CAAAuC,UAAA,KAAAsJ,kCAAA,mBA6JM;;;UA50BC7L,EAAA,CAAAK,SAAA,IAAa;UAAbL,EAAA,CAAAyC,UAAA,SAAA4I,GAAA,CAAA7K,OAAA,CAAa;UAuBbR,EAAA,CAAAK,SAAA,GAAW;UAAXL,EAAA,CAAAyC,UAAA,SAAA4I,GAAA,CAAA3K,KAAA,CAAW;UAuBRV,EAAA,CAAAK,SAAA,GAAa;UAAbL,EAAA,CAAAyC,UAAA,SAAA4I,GAAA,CAAAnE,OAAA,CAAa;UAchBlH,EAAA,CAAAK,SAAA,GAA0C;UAA1CL,EAAA,CAAAyC,UAAA,UAAA4I,GAAA,CAAAnE,OAAA,IAAAmE,GAAA,CAAA1G,aAAA,CAAA9D,MAAA,KAA0C;UA0Q1Cb,EAAA,CAAAK,SAAA,GAAc;UAAdL,EAAA,CAAAyC,UAAA,UAAA4I,GAAA,CAAAnE,OAAA,CAAc;UA4DdlH,EAAA,CAAAK,SAAA,GAA0C;UAA1CL,EAAA,CAAAyC,UAAA,UAAA4I,GAAA,CAAAnE,OAAA,IAAAmE,GAAA,CAAA1G,aAAA,CAAA9D,MAAA,KAA0C;UAuO1Cb,EAAA,CAAAK,SAAA,GAA4C;UAA5CL,EAAA,CAAAyC,UAAA,UAAA4I,GAAA,CAAAnE,OAAA,IAAAmE,GAAA,CAAA1G,aAAA,CAAA9D,MAAA,OAA4C;UAuEhDb,EAAA,CAAAK,SAAA,GAAyB;UAAzBL,EAAA,CAAAyC,UAAA,SAAA4I,GAAA,CAAAjE,mBAAA,CAAyB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}